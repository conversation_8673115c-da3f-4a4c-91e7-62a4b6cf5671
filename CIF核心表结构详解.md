# CIF模块核心表结构详解

## 1. 客户主表 (cif_client)

### 表结构概述
客户信息管理的核心主表，存储所有客户的基本信息。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| CLIENT_NO | varchar(20) | 客户号 | 主键，唯一标识客户 |
| CLIENT_NAME | varchar(200) | 客户名称 | 客户全称 |
| CH_CLIENT_NAME | varchar(200) | 客户中文名称 | 中文名称 |
| EN_CLIENT_NAME | varchar(200) | 客户英文名称 | 英文名称 |
| CLIENT_TYPE | varchar(3) | 客户类型 | 个人/企业/金融机构等 |
| CATEGORY_TYPE | varchar(3) | 客户细分类型 | 更详细的客户分类 |
| CLIENT_TRAN_STATUS | char(1) | 客户交易状态 | A-活动,C-已注销,D-休眠 |
| CLIENT_INDICATOR | char(1) | 客户标识 | N-普通,S-员工,V-VIP,M-潜在 |
| INLAND_OFFSHORE | char(1) | 境内境外标志 | I-境内,O-境外 |
| BLACKLIST_IND_FLAG | char(1) | 黑名单标志 | Y-是,N-否 |

### 业务特点
- 支持多语言客户名称
- 完整的客户状态管理
- 支持境内外客户区分
- 集成黑名单检查

## 2. 个人客户扩展表 (cif_client_indvl)

### 表结构概述
存储个人客户的详细信息，与客户主表一对一关系。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| CLIENT_NO | varchar(20) | 客户号 | 主键，关联客户主表 |
| CH_GIVEN_NAME | varchar(200) | 中文名 | 个人中文名 |
| CH_SURNAME | varchar(20) | 中文姓 | 个人中文姓氏 |
| GIVEN_NAME | varchar(200) | 英文名 | 个人英文名 |
| SURNAME | varchar(50) | 英文姓 | 个人英文姓氏 |
| SEX | char(1) | 性别 | M-男,F-女 |
| MARITAL_STATUS | varchar(2) | 婚姻状况 | D-离婚,M-已婚,S-单身,W-丧偶 |
| BIRTH_DATE | datetime | 出生日期 | 个人生日 |
| OCCUPATION_CODE | varchar(10) | 职业代码 | 职业分类 |
| EDUCATION | varchar(3) | 教育程度 | 学历编号 |
| MON_SALARY | decimal(17,2) | 月薪 | 月收入 |
| YEARLY_INCOME | decimal(17,2) | 年收入 | 年度收入 |

### 业务特点
- 支持中英文姓名分别存储
- 完整的个人基本信息
- 收入信息管理
- 职业和教育背景

## 3. 企业客户扩展表 (cif_client_corp)

### 表结构概述
存储企业客户的详细信息，包括注册信息、法人信息等。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| CLIENT_NO | varchar(20) | 客户号 | 主键，关联客户主表 |
| ORGAN | varchar(50) | 组织机构代码 | 企业组织机构代码 |
| REGISTER_NO | varchar(20) | 登记注册号 | 工商注册号等 |
| INCOR_DATE | datetime | 公司成立日期 | 企业成立时间 |
| LEGAL_REP | varchar(200) | 法定代表人 | 法人姓名 |
| REP_DOCUMENT_TYPE | varchar(5) | 法人证件类型 | 法人身份证件类型 |
| REP_DOCUMENT_ID | varchar(50) | 法人证件号码 | 法人身份证件号 |
| BUSINESS_SCOPE | varchar(200) | 经营范围 | 企业经营范围 |
| PAID_UP_CAPITAL | decimal(17,2) | 实收资本 | 实际缴纳资本 |
| AUTH_CAPITAL | decimal(17,2) | 注册资本 | 注册登记资本 |
| CORP_SIZE | varchar(5) | 企业规模 | 大中小微企业分类 |
| EMP_NUM | int | 员工数 | 企业员工数量 |

### 业务特点
- 完整的企业注册信息
- 法人代表信息管理
- 资本结构信息
- 企业规模分类

## 4. 客户证件表 (cif_client_document)

### 表结构概述
存储客户的各种证件信息，支持一个客户多个证件。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| CLIENT_NO | varchar(20) | 客户号 | 复合主键1 |
| DOCUMENT_TYPE | varchar(3) | 证件类型 | 复合主键2 |
| DOCUMENT_ID | varchar(50) | 证件号码 | 复合主键3 |
| ISS_COUNTRY | varchar(3) | 发证国家 | 复合主键4 |
| ISS_DATE | datetime | 签发日期 | 证件签发时间 |
| MATURITY_DATE | datetime | 到期日期 | 证件有效期 |
| ISSUE_BRANCH | varchar(50) | 签发机构 | 证件签发单位 |
| ISS_PLACE | varchar(200) | 签发地 | 证件签发地点 |
| PREF_FLAG | char(1) | 首选标志 | 是否为首选证件 |

### 业务特点
- 支持多证件管理
- 国际化证件支持
- 证件有效期管理
- 首选证件标识

## 5. 客户联系方式表 (cif_client_contact_tbl)

### 表结构概述
存储客户的各种联系方式信息，支持多种联系类型。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| CLIENT_NO | varchar(20) | 客户号 | 复合主键1 |
| CONTACT_TYPE | varchar(20) | 联系类型 | 复合主键2 |
| MOBILE_PHONE | varchar(50) | 移动电话 | 手机号码 |
| CONTACT_TEL | varchar(50) | 联系电话 | 固定电话 |
| EMAIL | varchar(200) | 电子邮件 | 邮箱地址 |
| ADDRESS | varchar(500) | 地址 | 详细地址 |
| COUNTRY | varchar(3) | 国家 | 国家代码 |
| STATE | varchar(10) | 省份 | 省份代码 |
| CITY | varchar(10) | 城市 | 城市代码 |
| POSTAL_CODE | varchar(10) | 邮政编码 | 邮编 |
| PREF_FLAG | char(1) | 首选标志 | 是否首选联系方式 |

### 业务特点
- 多种联系方式支持
- 完整的地址信息
- 国际化地址支持
- 首选联系方式管理

## 6. 客户冻结表 (cif_client_block)

### 表结构概述
管理客户的冻结和解冻操作记录。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| SEQ_NO | varchar(50) | 序号 | 主键 |
| CLIENT_NO | varchar(20) | 客户号 | 关联客户主表 |
| BLOCK_DATE | datetime | 冻结日期 | 冻结操作时间 |
| BLOCK_REASON | varchar(2) | 冻结原因 | 01-睡眠户,02-资料不全等 |
| BLOCK_USER_ID | varchar(30) | 冻结柜员 | 执行冻结的柜员 |
| UNFROZEN_FLAG | char(1) | 解冻标志 | Y-已解冻,N-未解冻 |
| UNBLOCK_DATE | datetime | 解冻日期 | 解冻操作时间 |
| UNBLOCK_USER_ID | varchar(30) | 解冻柜员 | 执行解冻的柜员 |

### 业务特点
- 完整的冻结解冻流程
- 操作员工记录
- 冻结原因分类管理
- 支持批量冻结

## 7. 渠道控制表 (cif_channel_control)

### 表结构概述
管理客户在不同渠道的访问控制和限制。

### 主要字段说明

| 字段名 | 类型 | 说明 | 备注 |
|--------|------|------|------|
| CONTROL_SEQ_NO | varchar(50) | 控制编号 | 主键 |
| CLIENT_NO | varchar(20) | 客户号 | 关联客户主表 |
| CONTROL_TYPE | varchar(3) | 控制类型 | 00-暂停非柜面,01-网银等 |
| CONTROL_STATUS | char(1) | 控制状态 | A-生效,E-终止,F-未生效 |
| START_DATE | datetime | 开始日期 | 限制开始时间 |
| END_DATE | datetime | 结束日期 | 限制结束时间 |
| LIMIT_LEVEL | varchar(5) | 限制级别 | CUST-客户级,ACCT-账户级 |

### 业务特点
- 精细化渠道控制
- 时间范围限制
- 多级别限制支持
- 状态生命周期管理

## 数据关系总结

### 主要关系链
1. **客户主体关系**: cif_client → cif_client_indvl/cif_client_corp
2. **客户证件关系**: cif_client → cif_client_document (一对多)
3. **客户联系关系**: cif_client → cif_client_contact_tbl (一对多)
4. **客户控制关系**: cif_client → cif_client_block/cif_channel_control (一对多)

### 设计优势
1. **数据完整性**: 通过主外键关系保证数据一致性
2. **扩展性**: 支持多证件、多联系方式等复杂业务场景
3. **国际化**: 支持多语言和多国家地区
4. **审计性**: 完整的操作记录和历史追踪
5. **灵活性**: 支持各种客户类型和业务场景
