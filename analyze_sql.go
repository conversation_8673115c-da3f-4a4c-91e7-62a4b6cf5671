package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"regexp"
	"sort"
	"strings"
)

type Table struct {
	Name        string   `json:"name"`
	Comment     string   `json:"comment"`
	Columns     []Column `json:"columns"`
	PrimaryKeys []string `json:"primary_keys"`
	Indexes     []Index  `json:"indexes"`
	Engine      string   `json:"engine"`
	Charset     string   `json:"charset"`
	Schema      string   `json:"schema"`
}

type Column struct {
	Name         string `json:"name"`
	Type         string `json:"type"`
	Length       string `json:"length,omitempty"`
	NotNull      bool   `json:"not_null"`
	DefaultValue string `json:"default_value,omitempty"`
	Comment      string `json:"comment"`
}

type Index struct {
	Name    string   `json:"name"`
	Type    string   `json:"type"`
	Columns []string `json:"columns"`
}

type Relationship struct {
	FromTable  string `json:"from_table"`
	FromColumn string `json:"from_column"`
	ToTable    string `json:"to_table"`
	ToColumn   string `json:"to_column"`
	Type       string `json:"type"`
}

type DatabaseSchema struct {
	Tables        []Table        `json:"tables"`
	Relationships []Relationship `json:"relationships"`
	Summary       SchemaSummary  `json:"summary"`
}

type SchemaSummary struct {
	TotalTables      int                 `json:"total_tables"`
	TablesByCategory map[string][]string `json:"tables_by_category"`
	CoreTables       []string            `json:"core_tables"`
	ConfigTables     []string            `json:"config_tables"`
	LogTables        []string            `json:"log_tables"`
	BatchTables      []string            `json:"batch_tables"`
}

func main() {
	// 分析两个SQL文件
	files := []string{"ens_cif.sql", "ens_cif001.sql"}

	var allTables []Table

	for _, filename := range files {
		fmt.Printf("分析文件: %s\n", filename)
		tables, err := parseSQLFile(filename)
		if err != nil {
			log.Printf("解析文件 %s 失败: %v", filename, err)
			continue
		}

		// 设置schema信息
		schema := "ens_cif"
		if strings.Contains(filename, "001") {
			schema = "ens_cif001"
		}

		for i := range tables {
			tables[i].Schema = schema
		}

		allTables = append(allTables, tables...)
		fmt.Printf("从 %s 解析出 %d 个表\n", filename, len(tables))
	}

	// 分析表关系
	relationships := analyzeRelationships(allTables)

	// 生成汇总信息
	summary := generateSummary(allTables)

	// 创建完整的数据库架构
	schema := DatabaseSchema{
		Tables:        allTables,
		Relationships: relationships,
		Summary:       summary,
	}

	// 输出结果
	fmt.Printf("\n=== CIF模块数据结构分析 ===\n")
	fmt.Printf("总表数量: %d\n", len(allTables))
	fmt.Printf("发现关系: %d\n", len(relationships))

	// 按分类输出表信息
	fmt.Println("\n=== 表分类汇总 ===")
	for category, tables := range summary.TablesByCategory {
		fmt.Printf("\n【%s】(%d个表):\n", category, len(tables))
		for _, table := range tables {
			fmt.Printf("  • %s\n", table)
		}
	}

	// 保存详细分析结果到JSON文件
	jsonData, err := json.MarshalIndent(schema, "", "  ")
	if err != nil {
		log.Printf("生成JSON失败: %v", err)
	} else {
		err = os.WriteFile("cif_database_schema.json", jsonData, 0644)
		if err != nil {
			log.Printf("写入JSON文件失败: %v", err)
		} else {
			fmt.Println("\n详细数据结构信息已保存到 cif_database_schema.json 文件")
		}
	}

	// 生成ER图的Mermaid代码
	generateERDiagram(allTables, relationships)
}

func parseSQLFile(filename string) ([]Table, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var tables []Table
	var currentTable *Table

	scanner := bufio.NewScanner(file)
	inTableDef := false

	// 正则表达式
	createTableRe := regexp.MustCompile("CREATE TABLE\\s+(?:IF NOT EXISTS\\s+)?`([^`]+)`\\s*\\(")
	columnRe := regexp.MustCompile("^\\s*`([^`]+)`\\s+([^\\s,]+)(?:\\(([^)]+)\\))?\\s*(.*)")
	primaryKeyRe := regexp.MustCompile("PRIMARY KEY\\s*\\(([^)]+)\\)")
	indexRe := regexp.MustCompile("(?:KEY|INDEX)\\s+`([^`]+)`\\s*\\(([^)]+)\\)")
	commentRe := regexp.MustCompile("COMMENT\\s*=\\s*'([^']*)'")
	engineRe := regexp.MustCompile("ENGINE\\s*=\\s*(\\w+)")
	charsetRe := regexp.MustCompile("CHARSET\\s*=\\s*(\\w+)")

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 检查是否是CREATE TABLE语句
		if matches := createTableRe.FindStringSubmatch(line); matches != nil {
			if currentTable != nil {
				tables = append(tables, *currentTable)
			}

			currentTable = &Table{
				Name:    matches[1],
				Columns: []Column{},
				Indexes: []Index{},
			}
			inTableDef = true
			continue
		}

		if !inTableDef || currentTable == nil {
			continue
		}

		// 检查表结束
		if strings.HasPrefix(line, ") ENGINE=") {
			// 解析表级别的属性
			if matches := commentRe.FindStringSubmatch(line); matches != nil {
				currentTable.Comment = matches[1]
			}
			if matches := engineRe.FindStringSubmatch(line); matches != nil {
				currentTable.Engine = matches[1]
			}
			if matches := charsetRe.FindStringSubmatch(line); matches != nil {
				currentTable.Charset = matches[1]
			}

			tables = append(tables, *currentTable)
			currentTable = nil
			inTableDef = false
			continue
		}

		// 解析列定义
		if matches := columnRe.FindStringSubmatch(line); matches != nil {
			column := Column{
				Name: matches[1],
				Type: matches[2],
			}

			if matches[3] != "" {
				column.Length = matches[3]
			}

			rest := matches[4]
			column.NotNull = strings.Contains(rest, "NOT NULL")

			// 提取默认值
			if defaultMatch := regexp.MustCompile(`DEFAULT\s+([^\s,]+)`).FindStringSubmatch(rest); defaultMatch != nil {
				column.DefaultValue = strings.Trim(defaultMatch[1], "'\"")
			}

			// 提取注释
			if commentMatch := regexp.MustCompile(`COMMENT\s+'([^']*)'`).FindStringSubmatch(rest); commentMatch != nil {
				column.Comment = commentMatch[1]
			}

			currentTable.Columns = append(currentTable.Columns, column)
			continue
		}

		// 解析主键
		if matches := primaryKeyRe.FindStringSubmatch(line); matches != nil {
			keys := strings.Split(matches[1], ",")
			for _, key := range keys {
				key = strings.Trim(strings.Trim(key, "`"), " ")
				currentTable.PrimaryKeys = append(currentTable.PrimaryKeys, key)
			}
			continue
		}

		// 解析索引
		if matches := indexRe.FindStringSubmatch(line); matches != nil {
			indexName := matches[1]
			columns := strings.Split(matches[2], ",")
			var indexColumns []string
			for _, col := range columns {
				col = strings.Trim(strings.Trim(col, "`"), " ")
				indexColumns = append(indexColumns, col)
			}

			index := Index{
				Name:    indexName,
				Type:    "INDEX",
				Columns: indexColumns,
			}
			currentTable.Indexes = append(currentTable.Indexes, index)
		}
	}

	return tables, scanner.Err()
}

func analyzeRelationships(tables []Table) []Relationship {
	var relationships []Relationship

	// 创建表名到表的映射
	tableMap := make(map[string]Table)
	for _, table := range tables {
		tableMap[table.Name] = table
	}

	// 分析外键关系（基于命名约定）
	for _, table := range tables {
		for _, column := range table.Columns {
			// 检查是否是外键（以_NO结尾且不是主键）
			if strings.HasSuffix(column.Name, "_NO") && column.Name != "SEQ_NO" {
				// 尝试找到对应的主表
				possibleTableName := strings.TrimSuffix(column.Name, "_NO")

				// 查找可能的主表
				for _, targetTable := range tables {
					if strings.Contains(targetTable.Name, strings.ToUpper(possibleTableName)) {
						// 检查目标表是否有对应的主键
						for _, pk := range targetTable.PrimaryKeys {
							if pk == column.Name {
								relationships = append(relationships, Relationship{
									FromTable:  table.Name,
									FromColumn: column.Name,
									ToTable:    targetTable.Name,
									ToColumn:   pk,
									Type:       "FOREIGN_KEY",
								})
								break
							}
						}
					}
				}
			}
		}
	}

	return relationships
}

func generateSummary(tables []Table) SchemaSummary {
	summary := SchemaSummary{
		TotalTables:      len(tables),
		TablesByCategory: make(map[string][]string),
	}

	// 按表名前缀分类
	for _, table := range tables {
		tableName := table.Name

		// 确定分类
		var category string
		switch {
		case strings.HasPrefix(tableName, "cif_client"):
			category = "客户信息表"
		case strings.HasPrefix(tableName, "cif_"):
			category = "CIF业务表"
		case strings.HasPrefix(tableName, "fm_"):
			category = "框架管理表"
		case strings.HasPrefix(tableName, "batch_"):
			category = "批处理表"
		case strings.Contains(tableName, "_hist"):
			category = "历史表"
		case strings.Contains(tableName, "_log"):
			category = "日志表"
		case strings.Contains(tableName, "_param"):
			category = "参数配置表"
		default:
			category = "其他表"
		}

		summary.TablesByCategory[category] = append(summary.TablesByCategory[category], tableName)

		// 识别核心表
		if strings.Contains(tableName, "cif_client") && !strings.Contains(tableName, "_hist") && !strings.Contains(tableName, "_log") {
			summary.CoreTables = append(summary.CoreTables, tableName)
		}

		// 识别配置表
		if strings.Contains(tableName, "_param") || strings.Contains(tableName, "_conf") {
			summary.ConfigTables = append(summary.ConfigTables, tableName)
		}

		// 识别日志表
		if strings.Contains(tableName, "_log") || strings.Contains(tableName, "_hist") {
			summary.LogTables = append(summary.LogTables, tableName)
		}

		// 识别批处理表
		if strings.HasPrefix(tableName, "batch_") || strings.Contains(tableName, "_batch_") {
			summary.BatchTables = append(summary.BatchTables, tableName)
		}
	}

	// 排序
	for category := range summary.TablesByCategory {
		sort.Strings(summary.TablesByCategory[category])
	}
	sort.Strings(summary.CoreTables)
	sort.Strings(summary.ConfigTables)
	sort.Strings(summary.LogTables)
	sort.Strings(summary.BatchTables)

	return summary
}

func generateERDiagram(tables []Table, relationships []Relationship) {
	fmt.Println("\n=== 生成ER图Mermaid代码 ===")

	// 生成Mermaid ER图代码
	mermaidCode := "erDiagram\n"

	// 添加核心表
	coreTableNames := []string{
		"cif_client", "cif_client_indvl", "cif_client_corp",
		"cif_client_document", "cif_client_contact_tbl",
		"cif_client_block", "cif_channel_control",
	}

	for _, tableName := range coreTableNames {
		for _, table := range tables {
			if table.Name == tableName {
				mermaidCode += fmt.Sprintf("    %s {\n", strings.ToUpper(table.Name))

				// 添加主要字段
				for i, column := range table.Columns {
					if i >= 8 { // 限制显示字段数量
						break
					}
					fieldType := column.Type
					if column.Length != "" {
						fieldType += "(" + column.Length + ")"
					}

					keyIndicator := ""
					for _, pk := range table.PrimaryKeys {
						if pk == column.Name {
							keyIndicator = " PK"
							break
						}
					}

					mermaidCode += fmt.Sprintf("        %s %s%s\n",
						fieldType, column.Name, keyIndicator)
				}
				mermaidCode += "    }\n"
				break
			}
		}
	}

	// 添加关系
	for _, rel := range relationships {
		// 只显示核心表之间的关系
		fromCore := false
		toCore := false
		for _, coreName := range coreTableNames {
			if rel.FromTable == coreName {
				fromCore = true
			}
			if rel.ToTable == coreName {
				toCore = true
			}
		}

		if fromCore && toCore {
			mermaidCode += fmt.Sprintf("    %s ||--o{ %s : %s\n",
				strings.ToUpper(rel.ToTable),
				strings.ToUpper(rel.FromTable),
				rel.FromColumn)
		}
	}

	// 保存Mermaid代码到文件
	err := os.WriteFile("cif_er_diagram.mmd", []byte(mermaidCode), 0644)
	if err != nil {
		log.Printf("写入Mermaid文件失败: %v", err)
	} else {
		fmt.Println("ER图Mermaid代码已保存到 cif_er_diagram.mmd 文件")
	}
}
