

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for batch_online_check
-- ----------------------------
DROP TABLE IF EXISTS `batch_online_check`;
CREATE TABLE `batch_online_check` (
  `JOB_ID` varchar(50) NOT NULL COMMENT 'JOB_ID',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `OPERATION_ID` varchar(50) DEFAULT NULL COMMENT 'sonic执行序号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `STEP_DESC` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `FILE_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `TRAN_DATE` date NOT NULL COMMENT '交易日期',
  `RUN_DATE` date DEFAULT NULL COMMENT '交易日期',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_MD5` varchar(255) DEFAULT NULL COMMENT '文件MD5校验值',
  `START_TIME` datetime DEFAULT NULL COMMENT '流程开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '流程结束时间',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数',
  `SUCCESS_NUMBER` bigint DEFAULT NULL COMMENT '成功笔数',
  `FAILURE_NUMBER` bigint DEFAULT NULL COMMENT '失败笔数',
  `TRAN_STATUS` varchar(16) DEFAULT NULL COMMENT '业务处理状态 S成功 F失败 P处理中',
  `ERROR_CODE` varchar(6) DEFAULT NULL COMMENT '交易状态码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '交易状态描述信息',
  `HOST_IP` varchar(16) DEFAULT NULL COMMENT 'IP',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批量交易类型',
  `BRANCH_ID` varchar(20) DEFAULT NULL COMMENT '机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道',
  `SYS_HEAD` blob COMMENT '联机调批量请求头信息',
  `APP_HEAD` blob COMMENT '联机调批量应用头信息',
  `ATTR_JSON` blob COMMENT '联机请求报文',
  PRIMARY KEY (`BATCH_NO`,`STEP_TYPE`,`CHANNEL_SEQ_NO`,`JOB_ID`,`TRAN_DATE`) USING BTREE,
  KEY `idx1_check` (`BATCH_NO`,`STEP_TYPE`,`PROCESS_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记表(UPRGHT)';

-- ----------------------------
-- Table structure for batch_online_check_details
-- ----------------------------
DROP TABLE IF EXISTS `batch_online_check_details`;
CREATE TABLE `batch_online_check_details` (
  `JOB_ID` varchar(50) NOT NULL COMMENT 'JOB_ID',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `STEP_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'STEP_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `OPERATION_ID` varchar(50) DEFAULT NULL COMMENT 'sonic执行序号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `FILE_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `PROCESS_TYPE` varchar(20) DEFAULT NULL COMMENT '文件类型',
  `TRAN_DATE` date NOT NULL COMMENT '交易日期',
  `RUN_DATE` date DEFAULT NULL COMMENT '交易日期',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_MD5` varchar(255) DEFAULT NULL COMMENT '文件MD5校验值',
  `START_TIME` datetime DEFAULT NULL COMMENT '流程开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '流程结束时间',
  `TOTAL_NUMBER` bigint DEFAULT NULL COMMENT '文件总笔数',
  `SUCCESS_NUMBER` bigint DEFAULT NULL COMMENT '成功笔数',
  `FAILURE_NUMBER` bigint DEFAULT NULL COMMENT '失败笔数',
  `TRAN_STATUS` varchar(16) DEFAULT NULL COMMENT '业务处理状态 S:成功 F:失败 P 处理中',
  `ERROR_CODE` varchar(6) DEFAULT NULL COMMENT '交易状态码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '交易状态描述信息',
  `HOST_IP` varchar(16) DEFAULT NULL COMMENT 'IP',
  PRIMARY KEY (`JOB_ID`,`BATCH_NO`,`STEP_TYPE`,`TRAN_DATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='文件登记表明细(UPRGHT)';

-- ----------------------------
-- Table structure for batch_online_upload
-- ----------------------------
DROP TABLE IF EXISTS `batch_online_upload`;
CREATE TABLE `batch_online_upload` (
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'JOB_RUN_ID',
  `STEP_RUN_ID` varchar(50) DEFAULT NULL COMMENT 'STEP_RUN_ID',
  `CHANNEL_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '渠道流水号',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号',
  `STEP_TYPE` varchar(50) NOT NULL COMMENT '业务类型',
  `FILE_NAME` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `FILE_PATH` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `UPLOAD_PATH` varchar(255) DEFAULT NULL COMMENT '上传文件路径',
  `BATCH_CLASS` varchar(10) DEFAULT NULL COMMENT '批量交易类型',
  `BRANCH_ID` varchar(20) DEFAULT NULL COMMENT '机构代码',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道',
  PRIMARY KEY (`BATCH_NO`) USING BTREE,
  KEY `idx1_upload` (`BATCH_NO`),
  KEY `idx2_upload` (`BATCH_NO`,`STEP_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量文件上传登记表(UPRGHT)';

-- ----------------------------
-- Table structure for cif_analogue_client
-- ----------------------------
DROP TABLE IF EXISTS `cif_analogue_client`;
CREATE TABLE `cif_analogue_client` (
  `ANALOGUE_RULE_ID` varchar(50) NOT NULL COMMENT '相似规则编号|相似规则编号',
  `ANALAGUE_RULE_NAME` varchar(50) DEFAULT NULL COMMENT '相似规则名称|相似规则名称',
  `ANALOGUE_GROUP` varchar(50) NOT NULL COMMENT '相似组|相似组',
  `GROUP_BRANCH` varchar(50) NOT NULL COMMENT '相似组有权处理机构|相似组有权处理机构',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `EN_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户英文名称|客户英文名称',
  `ISS_DATE` datetime DEFAULT NULL COMMENT '签发日期|签发日期',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|证件的到期日期',
  `ISSUE_BRANCH` varchar(50) DEFAULT NULL COMMENT '签发机构|签发机构',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`ANALOGUE_RULE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='相似客户信息表|相似客户信息表';

-- ----------------------------
-- Table structure for cif_approval_param
-- ----------------------------
DROP TABLE IF EXISTS `cif_approval_param`;
CREATE TABLE `cif_approval_param` (
  `BL_DESC` varchar(50) DEFAULT NULL COMMENT '描述|描述',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SKIP_FLOW_COLUMN` varchar(200) DEFAULT NULL COMMENT '跳过流程字段|流程审批，当该字段不为空时跳过该流程',
  `EFFECT_FLAG` varchar(1) DEFAULT NULL COMMENT '是否生效标志|是否生效标志|Y-是,N-否',
  `ORDER_NUM` int NOT NULL COMMENT '排序 |排序 ',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `FLOW_NAME` varchar(50) NOT NULL COMMENT '流程名称|流程名称',
  PRIMARY KEY (`ORDER_NUM`,`SEQ_NO`,`FLOW_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户申请流程参数表';

-- ----------------------------
-- Table structure for cif_batch_open_client_details
-- ----------------------------
DROP TABLE IF EXISTS `cif_batch_open_client_details`;
CREATE TABLE `cif_batch_open_client_details` (
  `JOB_RUN_ID` varchar(50) NOT NULL COMMENT '批处理任务ID|批处理任务ID',
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|渠道类型',
  `OPEN_BRANCH` varchar(50) DEFAULT NULL COMMENT '开立机构|开立机构',
  `OPEN_DATE` datetime DEFAULT NULL COMMENT '开立日期|开立日期',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `EN_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户英文名称|客户英文名称',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `CATEGORY_TYPE` varchar(3) DEFAULT NULL COMMENT '客户细分类型|客户细分类型',
  `SEX` char(1) DEFAULT NULL COMMENT '性别|性别|M-男,F-女',
  `INLAND_OFFSHORE` char(1) DEFAULT NULL COMMENT '境内境外标志|境内境外标志|I-境内,O-境外',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `ISS_DATE` datetime DEFAULT NULL COMMENT '签发日期|签发日期',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|证件的到期日期',
  `OCCUPATION_CODE` varchar(10) DEFAULT NULL COMMENT '职业|职业',
  `CONTACT_TYPE` varchar(20) DEFAULT NULL COMMENT '联系类型|联系类型',
  `MOBILE_PHONE` varchar(50) DEFAULT NULL COMMENT '移动电话|移动电话',
  `PHONE_NO` varchar(20) DEFAULT NULL COMMENT '固定电话|固定电话',
  `EMAIL` varchar(200) DEFAULT NULL COMMENT '电子邮件|电子邮件',
  `LOCATION` varchar(200) DEFAULT NULL COMMENT '客户地址|客户地址',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `OPEN_STATUS` char(1) DEFAULT NULL COMMENT '开户状态|开户状态|N-未处理  ,F-失败  ,S-成功',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `BIRTH_DATE` datetime DEFAULT NULL COMMENT '出生日期|出生日期',
  `CLIENT_INDICATOR` char(1) DEFAULT NULL COMMENT '客户标识|客户标识|N-普通客户,S-银行员工客户,V-VIP客户,M-潜在客户',
  `CLIENT_OPTION` varchar(2) NOT NULL COMMENT '客户操作类型|客户操作类型|01-新增,02-修改,03-删除,04-激活',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话  ',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量开立客户详细信息表|批量开立客户详细信息表';

-- ----------------------------
-- Table structure for cif_cl_black_status_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_cl_black_status_detail`;
CREATE TABLE `cif_cl_black_status_detail` (
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `BALCK_STATUS` char(1) DEFAULT NULL COMMENT '黑灰名单启用状态|黑灰名单启用状态（1正常，0删除）|Y-正常 ,N-删除',
  `CHECK_REMARK` varchar(200) DEFAULT NULL COMMENT '核查备注|核查备注',
  `UPDATE_DATE` datetime DEFAULT NULL COMMENT '更新日期|更新日期',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`CLIENT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='黑名单状态信息表|配合贷款给信贷系统提供黑灰名单(补充客户名、证件类型、证件信息)';

-- ----------------------------
-- Table structure for cif_cl_client_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_cl_client_detail`;
CREATE TABLE `cif_cl_client_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_STATUS` varchar(3) DEFAULT NULL COMMENT '客户状态|客户状态',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='网贷客户信息表|网贷客户信息表';

-- ----------------------------
-- Table structure for cif_cl_client_detail_hist
-- ----------------------------
DROP TABLE IF EXISTS `cif_cl_client_detail_hist`;
CREATE TABLE `cif_cl_client_detail_hist` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `CLIENT_STATUS` varchar(3) DEFAULT NULL COMMENT '客户状态|客户状态',
  `RET_STATUS` varchar(2) DEFAULT NULL COMMENT '结果状态|结果状态|CS-贷方成功,CF-贷方失败,DS-借方成功,DF-借方失败,F-失败,S-成功,P-预处理',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`,`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='网贷客户信息明细历史表|网贷客户信息明细历史表';

-- ----------------------------
-- Table structure for cif_cl_file_register
-- ----------------------------
DROP TABLE IF EXISTS `cif_cl_file_register`;
CREATE TABLE `cif_cl_file_register` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `EXE_ID` varchar(50) DEFAULT NULL COMMENT '执行ID|执行ID',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `FILE_CLASS` varchar(5) DEFAULT NULL COMMENT '文件种类|文件种类',
  `FILE_NAME` varchar(200) DEFAULT NULL COMMENT '文件名称|文件名称',
  `FILE_PATH` varchar(200) DEFAULT NULL COMMENT '文件路径|文件路径',
  `FILE_DESC` varchar(50) DEFAULT NULL COMMENT '文件描述|文件描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='网贷客户信息文件登记表|网贷客户信息文件登记表';

-- ----------------------------
-- Table structure for cif_client_contact_sync_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_contact_sync_detail`;
CREATE TABLE `cif_client_contact_sync_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `COUNTRY_CODE` varchar(20) NOT NULL COMMENT '国家代码|国家代码',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|所属机构号',
  `SERIAL_NO` varchar(50) NOT NULL COMMENT '业务流水号|支付流水号',
  `ADDRESS_ID` varchar(20) DEFAULT NULL COMMENT '地址编号|地址编号',
  `ADDRESS` varchar(500) DEFAULT NULL COMMENT '地址|地址',
  `ADDRESS1` varchar(500) DEFAULT NULL COMMENT '地址1|地址1 客户详细地址',
  `ADDRESS2` varchar(500) DEFAULT NULL COMMENT '地址2|地址2',
  `ADDRESS3` varchar(500) DEFAULT NULL COMMENT '地址3|地址3',
  `ADDRESS4` varchar(500) DEFAULT NULL COMMENT '地址4|地址4',
  `CONTACT_MAN` varchar(50) DEFAULT NULL COMMENT '联系人|联系人',
  `LINKMAN_NAME` varchar(200) DEFAULT NULL COMMENT '联系人名称|联系人名称',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话  ',
  `MOBILE_PHONE` varchar(50) DEFAULT NULL COMMENT '移动电话|移动电话',
  `MOBILE_PHONE2` varchar(50) DEFAULT NULL COMMENT '移动电话2|移动电话2',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `CBBK` varchar(10) DEFAULT NULL COMMENT '代理银行编号|代理银行编号',
  `CBBH` varchar(10) DEFAULT NULL COMMENT '代理机构号|代理机构号',
  `EMAIL` varchar(200) DEFAULT NULL COMMENT '电子邮件|电子邮件',
  `DESPATCH_CODE` varchar(2) DEFAULT NULL COMMENT '发送代码|发送代码',
  `COUNTRY` varchar(3) DEFAULT NULL COMMENT '国家|国家',
  `LAST_CHANGE_USER_ID` varchar(30) DEFAULT NULL COMMENT '最后修改柜员|最后修改柜员',
  `LAST_CHANGE_DATE` datetime DEFAULT NULL COMMENT '最后修改日期|最后修改日期',
  `LAST_CHANGE_TIME` varchar(26) DEFAULT NULL COMMENT '上次修改时间|上次修改时间',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `DEAL_STATUS` char(1) NOT NULL COMMENT '处理状态|处理状态|P-待处理，S-处理成功，F-处理失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`),
  KEY `IDX_CIF_CLIENT_CONTACT_SYNC_DETAIL_1m` (`BATCH_NO`,`DEAL_STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量客户联系信息同步登记簿|批量客户联系信息同步登记簿，用于同步汇丰客户联系信息';

-- ----------------------------
-- Table structure for cif_client_credit_sync_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_credit_sync_detail`;
CREATE TABLE `cif_client_credit_sync_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `COUNTRY_CODE` varchar(20) NOT NULL COMMENT '国家代码|国家代码',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|所属机构号',
  `SERIAL_NO` varchar(50) NOT NULL COMMENT '业务流水号|支付流水号',
  `CR_RATING` varchar(3) DEFAULT NULL COMMENT '客户信用等级|客户等级 客户信用等级|001-1级,002-2级,003-3级,004-4级 ',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `DEAL_STATUS` char(1) NOT NULL COMMENT '处理状态|处理状态|P-待处理，S-处理成功，F-处理失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`),
  KEY `IDX_CIF_CLIENT_CREDIT_SYNC_DETAIL_1m` (`BATCH_NO`,`CLIENT_NO`,`DEAL_STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量客户信用等级信息同步|批量客户信用等级信息同步，用于同步汇丰客户的信用等级信息';

-- ----------------------------
-- Table structure for cif_client_due_card_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_due_card_detail`;
CREATE TABLE `cif_client_due_card_detail` (
  `BATCH_NO` varchar(50) DEFAULT NULL COMMENT '批次号|批次号',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '限制类型|限制类型',
  `RESTRAINT_TYPE_DESC` varchar(200) DEFAULT NULL COMMENT '限制类型描述|限制类型描述',
  `BATCH_STATUS` char(1) DEFAULT NULL COMMENT '批次处理状态|批次处理状态|N-新建,S-成功,F-失败',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `RET_MSG` varchar(2000) DEFAULT NULL COMMENT '服务状态描述|服务状态描述',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `DOC_IS_EXPIRE` char(1) DEFAULT NULL COMMENT '证件是否即将到期|证件是否即将到期|0-增加,1-解除',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户证件到期明细表|证件到期,增加限制信息明细';

-- ----------------------------
-- Table structure for cif_client_info_sync_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_info_sync_detail`;
CREATE TABLE `cif_client_info_sync_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `COUNTRY_CODE` varchar(20) NOT NULL COMMENT '国家代码|国家代码',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|法人',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|所属机构号',
  `SERIAL_NO` varchar(50) NOT NULL COMMENT '业务流水号|支付流水号',
  `GHO_CUSTOMER_CLASS` varchar(5) DEFAULT NULL COMMENT '集团客户分类|集团客户分类',
  `EN_CLIENT_FULL_NAME` varchar(200) DEFAULT NULL COMMENT '客户全英文名称|客户全英文名称',
  `CLIENT_FULL_NAME` varchar(200) DEFAULT NULL COMMENT '客户全名称|客户全名称',
  `EN_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户英文名称|客户英文名称',
  `CH_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户中文名称|客户中文名称',
  `EN_CLIENT_SHORT` varchar(100) DEFAULT NULL COMMENT '客户英文简称|客户英文简称',
  `CLIENT_SHORT` varchar(200) DEFAULT NULL COMMENT '客户简称|客户简称',
  `MARKET_SECTOR1` varchar(10) DEFAULT NULL COMMENT '市场部门1|市场部门1',
  `MARKET_SECTOR2` varchar(10) DEFAULT NULL COMMENT '市场部门2|市场部门2',
  `MARKET_SECTOR3` varchar(10) DEFAULT NULL COMMENT '市场部门3|市场部门3',
  `MARKET_SECTOR_PROPORTION1` varchar(3) DEFAULT NULL COMMENT '市场部门1比例|市场部门1比例',
  `MARKET_SECTOR_PROPORTION2` varchar(3) DEFAULT NULL COMMENT '市场部门2比例|市场部门2比例',
  `MARKET_SECTOR_PROPORTION3` varchar(3) DEFAULT NULL COMMENT '市场部门3比例|市场部门3比例',
  `COUNTRY_LOC` varchar(3) DEFAULT NULL COMMENT '国籍|国籍',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `MATURITY_DATE` datetime DEFAULT NULL COMMENT '到期日期|到期日期',
  `COUNTRY_CITIZEN` varchar(3) DEFAULT NULL COMMENT '居住国家|居住国家',
  `LANGUAGE_CODE` char(1) DEFAULT NULL COMMENT '用户语言|用户语言|E-英文,C-中文',
  `GIMIS_CUSTOMER_INDICATOR` varchar(2) DEFAULT NULL COMMENT 'GIMIS客户标识|GIMIS客户标识',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|创建日期',
  `ADVICE_REQUIRED_CODE` varchar(2) DEFAULT NULL COMMENT '通知代码|通知代码',
  `CBBK` varchar(10) DEFAULT NULL COMMENT '代理银行编号|代理银行编号',
  `CBBH` varchar(10) DEFAULT NULL COMMENT '代理机构号|代理机构号',
  `CENTRAL_BANK_CUSTOMER_CLASS` varchar(5) DEFAULT NULL COMMENT '央行客户分类|央行客户分类',
  `INDUSTRY` varchar(20) DEFAULT NULL COMMENT '通用行业代码|通用行业代码',
  `IS_DELETABLE_FLAG` char(1) DEFAULT NULL COMMENT '是否可被删除标志|是否可被删除标志|Y-是,N-否',
  `ACCT_EXEC` varchar(30) DEFAULT NULL COMMENT '客户经理|客户经理',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `LAST_UPDATE_DATE` datetime DEFAULT NULL COMMENT '上次更新日期|上次更新日期',
  `LAST_CHANGE_TIME` varchar(26) DEFAULT NULL COMMENT '上次修改时间|上次修改时间',
  `LAST_CHANGE_USER_ID` varchar(30) DEFAULT NULL COMMENT '最后修改柜员|最后修改柜员',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `DEAL_STATUS` char(1) NOT NULL COMMENT '处理状态|处理状态|P-待处理，S-处理成功，F-处理失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `JOB_RUN_ID` varchar(50) DEFAULT NULL COMMENT '批处理任务ID|批处理任务ID',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`),
  KEY `IDX_CIF_CLIENT_INFO_SYNC_DETAIL_1m` (`BATCH_NO`,`CLIENT_NO`,`DEAL_STATUS`,`IS_DELETABLE_FLAG`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='批量客户信息同步登记簿|批量客户信息同步登记簿，用于同步汇丰客户信息';

-- ----------------------------
-- Table structure for cif_client_joint_sync_detail
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_joint_sync_detail`;
CREATE TABLE `cif_client_joint_sync_detail` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `BATCH_SEQ_NO` varchar(50) NOT NULL COMMENT '批次明细序号|批次明细序号',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|客户号',
  `SEQ_NO` varchar(50) DEFAULT NULL COMMENT '序号|序号',
  `COUNTRY_CODE` varchar(20) NOT NULL COMMENT '国家代码|联名客户国家代码',
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|联名客户所属法人',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|联名客户所属机构号',
  `SERIAL_NO` varchar(50) NOT NULL COMMENT '业务流水号|联名客户序号',
  `SUB_CLIENT_NO` varchar(20) NOT NULL COMMENT '子客户号|关联方客户号',
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|关联客户国家代码',
  `SUB_COMPANY` varchar(20) NOT NULL COMMENT '子公司法人  |关联客户公司所属法人',
  `SUB_BRANCH_CODE` varchar(50) NOT NULL COMMENT '分行代码|关联客户所属机构',
  `SUB_SERIAL_NO` varchar(50) NOT NULL COMMENT '子序列号|关联客户序号',
  `ASYN_DATE` datetime DEFAULT NULL COMMENT '同步日期|同步日期',
  `DEAL_STATUS` char(1) NOT NULL COMMENT '处理状态|处理状态|P-待处理，S-处理成功，F-处理失败',
  `ERROR_CODE` varchar(50) DEFAULT NULL COMMENT '错误码|错误码',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BATCH_NO`,`BATCH_SEQ_NO`),
  KEY `IDX_CIF_CLIENT_JOINT_SYNC_DETAIL_1M` (`BATCH_NO`,`CLIENT_NO`,`DEAL_STATUS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='联名客户关系同步信息表|联名客户关系同步信息表，用于同步联名客户关系信息';

-- ----------------------------
-- Table structure for cif_client_merge
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_merge`;
CREATE TABLE `cif_client_merge` (
  `MERGE_NO` varchar(50) NOT NULL COMMENT '合并编号|合并编号',
  `MERGE_DATE` datetime DEFAULT NULL COMMENT '合并日期|合并日期',
  `OLD_CLIENT_NO` varchar(20) NOT NULL COMMENT '原客户号|原客户号',
  `NEW_CLIENT_NO` varchar(20) NOT NULL COMMENT '新客户号|新客户号',
  `OLD_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '原客户名称|原客户名称',
  `NEW_CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '新客户名称|新客户名称',
  `MERGE_FLAG` varchar(2) DEFAULT NULL COMMENT '是否合并标志|合并状态|C-待合并,CS-检查通过,CF-检查失败 ,S-成功,F-失败 ,R-合并撤销',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|描述',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  PRIMARY KEY (`MERGE_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户合并信息表|客户合并信息表';

-- ----------------------------
-- Table structure for cif_client_recognize
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_recognize`;
CREATE TABLE `cif_client_recognize` (
  `INCREASE_ID` varchar(30) NOT NULL COMMENT '自增ID|自增ID',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `CLIENT_NAME` varchar(200) DEFAULT NULL COMMENT '客户名称|客户名称',
  `CLIENT_DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户证件类型|客户证件类型',
  `PHONE` varchar(20) DEFAULT NULL COMMENT '手机号|手机号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `REGISTER_NO` varchar(20) DEFAULT NULL COMMENT '登记注册号|登记注册号',
  `RECOGNIZE_RULE` char(1) DEFAULT NULL COMMENT '客户相似度识别规则|客户相似度识别规则|1-证件类型证件号相同,2-客户姓名证件号相同,3-客户姓名证件号证件类型都相同,4-客户姓名手机号相同,5-组织机构代码相同,6-注册登记号相同,7-营业执照号相同,8-统一社会信用代码相同',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`INCREASE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户相似记录表|记录客户相似度类型信息表';

-- ----------------------------
-- Table structure for cif_client_shortmsg_para
-- ----------------------------
DROP TABLE IF EXISTS `cif_client_shortmsg_para`;
CREATE TABLE `cif_client_shortmsg_para` (
  `MSG_PARA_ID` varchar(50) NOT NULL COMMENT '短信提醒编号|短信提醒编号',
  `SHORTMSG_ID` varchar(10) NOT NULL COMMENT '短信模板ID|短信模板ID',
  `PARA_ID_DESC` varchar(50) NOT NULL COMMENT '短信提醒描述|短信提醒描述',
  `MESSAGE` varchar(2000) DEFAULT NULL COMMENT '消息内容|消息内容',
  `REMIND_DAYS` int DEFAULT NULL COMMENT '提前提醒天数|距指定日期提前多少天就发短信',
  `FIX_DAY` varchar(2) DEFAULT NULL COMMENT '固定发送日|固定发送日,每月的哪一天发送',
  `INTERVAL_DAYS` int DEFAULT NULL COMMENT '间隔发送天数|间隔发送天数,每间隔几天发送',
  `DAYS_AFTER` int DEFAULT NULL COMMENT '证件到期后天数|距指定日期推后多少天就停发短信',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`MSG_PARA_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户短信提醒参数表|客户短信提醒参数表';

-- ----------------------------
-- Table structure for cif_define_date_information
-- ----------------------------
DROP TABLE IF EXISTS `cif_define_date_information`;
CREATE TABLE `cif_define_date_information` (
  `COLUMN_CODE` varchar(50) NOT NULL COMMENT '字段编码|取值范围',
  `COLUMN_NAME` varchar(50) DEFAULT NULL COMMENT '列名|列名',
  `COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '取值范围|取值范围',
  `COLUMN_TYPE` varchar(10) DEFAULT NULL COMMENT '字段类型|字段类型',
  `COLUMN_LENGTH` varchar(5) DEFAULT NULL COMMENT '字段长度|字段长度',
  `APPLICATION_FLAG` varchar(5) DEFAULT NULL COMMENT '应用标志|应用标志 Cif-客户，Acct-账户',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COLUMN_VALUE_DESC` varchar(100) DEFAULT NULL COMMENT '取值范围描述|取值范围描述',
  PRIMARY KEY (`COLUMN_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='自定义客户信息参数表|保存参数平台发送的用户自定义参数';

-- ----------------------------
-- Table structure for cif_tran_control_hist
-- ----------------------------
DROP TABLE IF EXISTS `cif_tran_control_hist`;
CREATE TABLE `cif_tran_control_hist` (
  `CHANNEL_SEQ_NO` varchar(50) NOT NULL COMMENT '渠道流水号|渠道流水号',
  `SUB_SEQ_NO` varchar(100) NOT NULL COMMENT '子流水号|子流水号',
  `BUS_SEQ_NO` varchar(50) DEFAULT NULL COMMENT '业务流水号|华兴项目使用的业务流水号字段',
  `SOURCE_TYPE` varchar(10) NOT NULL COMMENT '渠道类型|渠道类型',
  `CHANNEL_DATE` datetime NOT NULL COMMENT '渠道日期|渠道日期',
  `SOURCE_MODULE` varchar(3) DEFAULT NULL COMMENT '源模块|源模块',
  `TRAN_BRANCH` varchar(50) DEFAULT NULL COMMENT '交易机构|交易机构',
  `REFERENCE` varchar(50) DEFAULT NULL COMMENT '交易参考号|交易参考号',
  `SERVICE_CODE` varchar(20) DEFAULT NULL COMMENT '服务代码|服务代码',
  `MESSAGE_TYPE` varchar(10) DEFAULT NULL COMMENT '接口服务类型|接口服务类型',
  `MESSAGE_CODE` varchar(10) DEFAULT NULL COMMENT '接口服务代码|接口服务代码',
  `TRAN_STATUS` char(1) DEFAULT NULL COMMENT '业务处理状态|业务处理状态',
  `CLIENT_NO` varchar(20) DEFAULT NULL COMMENT '客户号|客户号',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`CHANNEL_SEQ_NO`,`SUB_SEQ_NO`,`SOURCE_TYPE`,`CHANNEL_DATE`),
  KEY `IDX_CIF_TRAN_CONTROL_HIST_1M` (`REFERENCE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='交易流程控制表|交易流程控制表';

-- ----------------------------
-- Table structure for cif_user_access
-- ----------------------------
DROP TABLE IF EXISTS `cif_user_access`;
CREATE TABLE `cif_user_access` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|交易柜员',
  `BUSINESS_LIST` varchar(200) DEFAULT NULL COMMENT '行业代码组|行业代码组，分隔符分开',
  `OCCUPATION_CODE_LIST` varchar(200) DEFAULT NULL COMMENT '职业组|多个职业代码，分隔符分开',
  `CLIENT_TYPE_LIST` varchar(200) DEFAULT NULL COMMENT '客户类型组|客户类型组，分隔符分开',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='柜员权限表|柜员权限功能';

-- ----------------------------
-- Table structure for flyway_schema_history
-- ----------------------------
DROP TABLE IF EXISTS `flyway_schema_history`;
CREATE TABLE `flyway_schema_history` (
  `installed_rank` int NOT NULL,
  `version` varchar(50) DEFAULT NULL,
  `description` varchar(200) NOT NULL,
  `type` varchar(20) NOT NULL,
  `script` varchar(1000) NOT NULL,
  `checksum` int DEFAULT NULL,
  `installed_by` varchar(100) NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`),
  KEY `flyway_schema_history_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Table structure for fm_acct_exec
-- ----------------------------
DROP TABLE IF EXISTS `fm_acct_exec`;
CREATE TABLE `fm_acct_exec` (
  `ACCT_EXEC` varchar(30) NOT NULL COMMENT '客户经理|客户经理',
  `ACCT_EXEC_NAME` varchar(200) NOT NULL COMMENT '客户经理姓名|客户经理姓名',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `ISS_COUNTRY` varchar(3) DEFAULT NULL COMMENT '发证国家|发证国家',
  `ACCT_EXEC_TYPE` varchar(3) DEFAULT NULL COMMENT '客户经理类型|客户经理类型',
  `ACCT_EXEC_STATUS` char(1) DEFAULT NULL COMMENT '客户经理状态|客户经理状态|A-活动,C-失效',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `COLLAT_MGR_IND` char(1) DEFAULT NULL COMMENT '是否担保经理|是否担保经理|Y-是,N-否',
  `CONTACT_ID` varchar(100) DEFAULT NULL COMMENT '联系类型ID|联系类型',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话  ',
  `MANAGER` varchar(30) DEFAULT NULL COMMENT '主管经理|主管经理',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心 ',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`ACCT_EXEC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户经理表|客户经理表';

-- ----------------------------
-- Table structure for fm_backup_clean_param
-- ----------------------------
DROP TABLE IF EXISTS `fm_backup_clean_param`;
CREATE TABLE `fm_backup_clean_param` (
  `BASE_TABLE_NAME` varchar(100) NOT NULL COMMENT '业务表|业务表',
  `HIST_TABLE_NAME` varchar(100) DEFAULT NULL COMMENT '历史表|历史表',
  `FREQUENCY` varchar(20) DEFAULT NULL COMMENT '对应频率|对应频率',
  `NEXT_DATE` datetime DEFAULT NULL COMMENT '下一日期|下一处理日期',
  `BACKUP_TYPE` varchar(10) DEFAULT NULL COMMENT '备份类型（01-分区备份、02-条件备份、03-不备份）|备份类型（01-分区备份、02-条件备份、03-不备份）',
  `BACKUP_SEGMENT_FLAG` varchar(10) DEFAULT NULL COMMENT '备份分段(Y-是、N-否)|备份分段(Y-是、N-否)',
  `CLEAN_TYPE` varchar(10) DEFAULT NULL COMMENT '清理类型(01-分区清理、02-条件清理、03-不清理）|清理类型(01-分区清理、02-条件清理、03-不清理）',
  `CLEAN_SEGMENT_FLAG` varchar(10) DEFAULT NULL COMMENT '清理分段(Y-是、N-否)|清理分段(Y-是、N-否)',
  `FIELD_NAME` varchar(50) DEFAULT NULL COMMENT '字段名|字段名',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `BACKUP_SQL_ID` varchar(50) DEFAULT NULL COMMENT '备份SQLID|备份SQLID',
  `CLEAN_SQL_ID` varchar(50) DEFAULT NULL COMMENT '清理SQLID|清理SQLID',
  `BACKUP_STATUS` varchar(10) DEFAULT NULL COMMENT '备份状态(R – 备份中  E –备份结束  N-未开始)|备份状态(R – 备份中  E –备份结束)',
  `CLEAN_STATUS` varchar(2) DEFAULT NULL COMMENT '结清标志|结清标志',
  `BACKUP_START_TIME` datetime DEFAULT NULL COMMENT '备份起始时间|备份起始时间',
  `BACKUP_END_TIME` datetime DEFAULT NULL COMMENT '备份结束时间|备份结束时间',
  `CLEAN_START_TIME` datetime DEFAULT NULL COMMENT '清理起始时间|清理起始时间',
  `CLEAN_END_TIME` datetime DEFAULT NULL COMMENT '清理结束时间|清理结束时间',
  `BACKUP_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '备份分段ID|备份分段ID',
  `CLEAN_SEGMENT_ID` varchar(100) DEFAULT NULL COMMENT '清理分段ID|清理分段ID',
  `CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `RETAIN_TIME_LIMIT` varchar(20) DEFAULT NULL COMMENT '保留期限|保留期限',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`BASE_TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='备份清理参数配置表|配置核心数据库大表备份和清理的周期，处理方式，留存期限等参数';

-- ----------------------------
-- Table structure for fm_branch
-- ----------------------------
DROP TABLE IF EXISTS `fm_branch`;
CREATE TABLE `fm_branch` (
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|机构代码',
  `BRANCH_NAME` varchar(200) NOT NULL COMMENT '机构名称|机构名称',
  `BRANCH_SHORT` varchar(30) NOT NULL COMMENT '机构简称|机构简称',
  `BRANCH_TYPE` char(1) NOT NULL COMMENT '机构类型|区分机构是行内还是行外，目前默认为I-本行机构|I-本行,O-他行',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_BR_IND` char(1) DEFAULT NULL COMMENT '是否交易机构|只有交易行才能开门办理业务，接收上送报文请求，否则只能在核算和报表场景中使用；已被撤并的机构此字段也登记为N|Y-交易行,N-非交易行',
  `INTERNAL_CLIENT` varchar(20) DEFAULT NULL COMMENT '内部客户号|默认按机构编号在cif应用注册一套客户信息，用户核心系统内部业务场景使用，ecif无需管控，有OM初始化导入，后续机构新增时也会自动创建一套客户信息',
  `HIERARCHY_CODE` varchar(50) DEFAULT NULL COMMENT '层级代码|机构的总分支级别',
  `ATTACHED_TO` varchar(50) DEFAULT NULL COMMENT '所属上级|机构的上级管理机构',
  `SUB_BRANCH_CODE` varchar(50) DEFAULT NULL COMMENT '分行代码|默认配置机构编号前三位，业务系统实际暂未使用',
  `BRANCH_STATUS` char(1) DEFAULT NULL COMMENT '机构开关门状态|机构开关门状态|I-开门,O-关门,X-已撤并',
  `FTA_FLAG` char(1) DEFAULT NULL COMMENT '是否自贸区机构|是否自贸区机构|Y-是,N-否',
  `FTA_CODE` varchar(10) DEFAULT NULL COMMENT '自贸区代码|自贸区代码',
  `LOCAL_CCY` varchar(3) DEFAULT NULL COMMENT '当地币种|机构当地默认的币种，一般为人民币',
  `BASE_CCY` varchar(3) DEFAULT NULL COMMENT '基础币种|机构在系统中的默认基础币种，一般为人民币',
  `CCY_CTRL_BRANCH` varchar(50) DEFAULT NULL COMMENT '结售汇平盘机构|结售汇平盘机构',
  `CHEQUE_ISSUING_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '是否签发支票|机构是否允许签发出售支票|Y-支票发行行,N-非支票发行',
  `INT_TAX_LEVY` char(1) NOT NULL COMMENT '利息税征收标志|利息税征收标志|Y-是,N-否',
  `TAX_RPT_BRANCH` varchar(50) DEFAULT NULL COMMENT '税收机构（总账用）|税收机构（总账用）',
  `SURTAX_TYPE` varchar(30) DEFAULT NULL COMMENT '附加税类型|附加税的税率类型',
  `PBOC_FUND_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '人行备付金检查标志|是否检查人行备付金余额，目前系统暂未使用|Y-检查余额,N-不检查余额',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心',
  `IP_ADDR` varchar(200) DEFAULT NULL COMMENT '机构IP地址|机构下所有终端的IP地址，英文逗号分隔，目前系统暂未使用',
  `COUNTRY` varchar(3) NOT NULL COMMENT '国家|国家',
  `STATE` varchar(10) NOT NULL COMMENT '省别代码|省、州',
  `CITY` varchar(10) DEFAULT NULL COMMENT '城市|城市',
  `POSTAL_CODE` varchar(10) DEFAULT NULL COMMENT '邮政编码|邮政编码',
  `DISTRICT` varchar(10) DEFAULT NULL COMMENT '区号|区号',
  `AREA_CODE` varchar(5) DEFAULT NULL COMMENT '地区码|地区码',
  `CNY_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(人民币)|账套(人民币)，目前系统暂未使用',
  `HKD_BUSINESS_UNIT` varchar(10) DEFAULT NULL COMMENT '账套(港币)|账套(港币)，目前系统暂未使用',
  `FX_ORGAN_CODE` varchar(10) DEFAULT NULL COMMENT '外汇金融机构代码|外汇金融机构代码，目前系统暂未使用',
  `CREATE_DATE` datetime DEFAULT NULL COMMENT '创建日期|机构成立创建的日期，不大于正式开门营业的日期，仅登记',
  `START_DATE` datetime NOT NULL COMMENT '开始日期|机构参数启用开始日期，也是机构正式开门营业的日期',
  `END_DATE` datetime DEFAULT NULL COMMENT '结束日期|机构参数失效日期，为空默认永不失效，一般在机构撤并时会给被撤并机构设置此日期',
  `EOD_FLAG` char(1) DEFAULT NULL COMMENT '日终标识|表示当前机构是否处于日终状态，目前系统暂未使用|Y-日终状态,N-营业状态',
  `DEFAULT_TELLER_LOGIN` char(1) NOT NULL COMMENT '默认柜员登录认证方式|当前机构下柜员新增时默认的登录方式，目前系统暂未使用|1-密码,2-指纹,3-密码+指纹',
  `ABNORMAL_OPEN_CONTROL` char(1) DEFAULT NULL COMMENT '非正常时间开门控制方式|定义机构在非正常时间开门时的控制方式，目前系统暂未使用|1-不控制,2-拒绝,3-提醒',
  `OPER_MAX_LEVEL` varchar(5) DEFAULT NULL COMMENT '操作最高级别|目前系统暂未使用',
  `AUTH_FLAG` char(1) DEFAULT NULL COMMENT '授权标志|用于机构二次开门时，需要先授权，授权后此标志修改为Y，开门接口就可以正常开门了，目前系统暂未使用|Y-已授权,N-未授权',
  `TAILBOX_DETACH_FLAG` char(1) NOT NULL COMMENT '尾箱控制方式|柜员正式签退和网点日终时，对于没有脱离绑定关系的尾箱，根据此配置默认进行脱离处理|Y-必须上缴,N-不上缴,O-不控制',
  `VOUCHER_USER_CONTRAL_FLAG` char(1) DEFAULT NULL COMMENT '是否限制凭证入库柜员|行外和机构间凭证出入库时，是否必须由凭证库管操作|Y-限制,N-不限制',
  `ACCOUNTING_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '是否核算机构|当前机构是否是核算机构',
  `ACCOUNTING_BRANCH` varchar(50) DEFAULT NULL COMMENT '核算机构|当前机构对应的核算机构编号，可以是自己',
  `AUTO_CREATE_INTERNAL_ACCT_FLAG` char(1) DEFAULT NULL COMMENT '自动开立内部户标志|自动开立内部户标志|Y-是,N-否',
  `NORMAL_OPEN_TIME` varchar(8) DEFAULT NULL COMMENT '正常开门时间|配置机构在此时间之前不能对外开门营业，对应JAVA类型：HH:mm:ss，举例：13:25:45，目前系统暂未使用',
  `NORMAL_CLOSE_TIME` varchar(8) DEFAULT NULL COMMENT '正常关门时间|配置机构在此时间后不能再对外营业，对应JAVA类型：HH:mm:ss，举例：13:25:45，目前系统暂未使用',
  `CITY_BRANCH_FLAG` char(1) NOT NULL COMMENT '市区支行标志|市区支行标志|Y-是,N-否',
  `PBOC_FINANCING_NO` varchar(30) DEFAULT NULL COMMENT '人行金融机构编码|人行金融机构编码，目前系统暂未使用',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `BIC_CODE` varchar(10) DEFAULT NULL COMMENT 'BIC代码|BIC代码',
  PRIMARY KEY (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构信息表|机构信息表';

-- ----------------------------
-- Table structure for fm_branch_status_detail
-- ----------------------------
DROP TABLE IF EXISTS `fm_branch_status_detail`;
CREATE TABLE `fm_branch_status_detail` (
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `BRANCH` varchar(50) DEFAULT NULL COMMENT '所属机构号|机构代码',
  `REG_TYPE` char(1) DEFAULT NULL COMMENT '登记类型|登记类型|1-开户/卡,2-销户/卡',
  `REG_VALUE` varchar(50) DEFAULT NULL COMMENT '登记值|登记值',
  `TRAN_DATE` datetime DEFAULT NULL COMMENT '交易日期|交易日期',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='机构状态历史登记明细表|登记机构开关门历史记录';

-- ----------------------------
-- Table structure for fm_client_out_info
-- ----------------------------
DROP TABLE IF EXISTS `fm_client_out_info`;
CREATE TABLE `fm_client_out_info` (
  `OK_FLAG` varchar(1) DEFAULT NULL COMMENT '是否已完成|是否已完成|Y-是,N-否 ',
  `BUSI_DATA` blob COMMENT '业务数据|业务数据',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `CLASS_NAME` varchar(200) DEFAULT NULL COMMENT '分段映射实体类名|分段映射实体类名',
  `CHANGE_DATE` datetime DEFAULT NULL COMMENT '交换日期|交换日期',
  `OLD_CLIENT_NO` varchar(20) NOT NULL COMMENT '原客户号|原客户号',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名|表名',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `CLIENT_NO` varchar(20) NOT NULL COMMENT '客户号|新客户号',
  `MERGE_NO` varchar(50) NOT NULL COMMENT '合并编号|合并编号',
  PRIMARY KEY (`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='客户信息迁出表|客户信息迁出表';

-- ----------------------------
-- Table structure for fm_data_storage_reg
-- ----------------------------
DROP TABLE IF EXISTS `fm_data_storage_reg`;
CREATE TABLE `fm_data_storage_reg` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `DEAL_FLAG` char(1) DEFAULT NULL COMMENT '处理标识|处理标识|1-未处理  ,2-已处理 ',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TABLE_NAME`,`DEAL_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载入库文件处理结果表';

-- ----------------------------
-- Table structure for fm_data_unload_param
-- ----------------------------
DROP TABLE IF EXISTS `fm_data_unload_param`;
CREATE TABLE `fm_data_unload_param` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `MODULE_NAME` varchar(50) DEFAULT NULL COMMENT '模块名称|模块名称',
  `TARGET_SYSTEM` varchar(50) DEFAULT NULL COMMENT '目标系统|目标系统',
  `FREQUENCE` varchar(5) DEFAULT NULL COMMENT '频率|频率',
  PRIMARY KEY (`TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载参数配置表|用于数据据卸载参数配置';

-- ----------------------------
-- Table structure for fm_lang_translation
-- ----------------------------
DROP TABLE IF EXISTS `fm_lang_translation`;
CREATE TABLE `fm_lang_translation` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `TRANS_COLUMN` varchar(50) NOT NULL COMMENT '国际化字段|国际化字段',
  `TRANS_COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '国际化字段取值|国际化字段取值',
  `BUSI_KEY` varchar(200) DEFAULT NULL COMMENT '业务主键|业务主键',
  `BUSI_KEY_VALUE` varchar(500) NOT NULL COMMENT '业务主键取值|业务主键取值',
  `TRAN_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言|语言',
  PRIMARY KEY (`TABLE_NAME`,`TRANS_COLUMN`,`BUSI_KEY_VALUE`,`LANGUAGE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='业务参数多语言码值定义表|保存业务参数表中国际化字段在多语言中的取值信息';

-- ----------------------------
-- Table structure for fm_run_date_notice
-- ----------------------------
DROP TABLE IF EXISTS `fm_run_date_notice`;
CREATE TABLE `fm_run_date_notice` (
  `TRAN_DATE` datetime NOT NULL COMMENT '交易日期|交易日期',
  `SWITCH_YN` char(1) NOT NULL COMMENT '开关|是否为外部日期接受|Y-开,N-关 ',
  `NEXT_RUN_DATE` datetime DEFAULT NULL COMMENT '下一运行日|下一运行日',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`TRAN_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='营业日期通知表|营业日期通知表';

-- ----------------------------
-- Table structure for fm_service_access_conf
-- ----------------------------
DROP TABLE IF EXISTS `fm_service_access_conf`;
CREATE TABLE `fm_service_access_conf` (
  `SERVICE_ID` varchar(30) NOT NULL COMMENT '服务ID|服务ID',
  `AGENT_ACCOUNT_FLAG` char(1) DEFAULT NULL COMMENT '代理记账标志|接口是否支持柜员代理记账|Y-支持,N-不支持',
  PRIMARY KEY (`SERVICE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='代理记账可访问的接口配置表|代理记账可访问的接口配置表';

-- ----------------------------
-- Table structure for fm_sys_lang_translation
-- ----------------------------
DROP TABLE IF EXISTS `fm_sys_lang_translation`;
CREATE TABLE `fm_sys_lang_translation` (
  `LANGUAGE` varchar(10) NOT NULL COMMENT '语言|语言',
  `TRANS_COLUMN` varchar(50) NOT NULL COMMENT '国际化字段|国际化字段',
  `BUSI_KEY` varchar(200) NOT NULL COMMENT '业务主键|业务主键',
  `TRANS_COLUMN_VALUE` varchar(500) DEFAULT NULL COMMENT '国际化字段取值|国际化字段取值',
  PRIMARY KEY (`LANGUAGE`,`TRANS_COLUMN`,`BUSI_KEY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统参数多语言码值定义表|保存系统参数表中国际化字段在多语言中的取值信息';

-- ----------------------------
-- Table structure for fm_system
-- ----------------------------
DROP TABLE IF EXISTS `fm_system`;
CREATE TABLE `fm_system` (
  `COMPANY` varchar(20) NOT NULL COMMENT '法人|系统运行默认法人编号',
  `COY_NAME` varchar(50) NOT NULL COMMENT '银行全称|银行全称',
  `COY_SHORT` varchar(50) NOT NULL COMMENT '银行简称|银行简称',
  `DEFAULT_BRANCH` varchar(50) DEFAULT NULL COMMENT '默认机构|默认机构，一般为默认法人的总行清算中心',
  `LAST_RUN_DATE` datetime DEFAULT NULL COMMENT '上一运行日期|上一运行日期',
  `RUN_DATE` datetime NOT NULL COMMENT '运行日期|系统当前运行会计日期',
  `NEXT_RUN_DATE` datetime NOT NULL COMMENT '下一运行日|下一运行日',
  `MTH_END_DATE` datetime NOT NULL COMMENT '本月月末日期|本月月末日期',
  `QUR_END_DATE` datetime NOT NULL COMMENT '季末日期|季末日期',
  `HALF_END_DATE` datetime NOT NULL COMMENT '半年末日期|半年末日期',
  `YR_END_DATE` datetime NOT NULL COMMENT '本年年末日期|本年年末日期',
  `MAIN_BRANCH_CODE` varchar(50) DEFAULT NULL COMMENT '总行层级代码|总行层级代码，目前系统暂未使用',
  `HEAD_OFFICE_CLIENT` varchar(20) DEFAULT NULL COMMENT '总行清算行内部客户|总行清算行内部客户，目前系统暂未使用',
  `SYSTEM_PHASE` varchar(3) NOT NULL COMMENT '系统所处的阶段|当前系统所处的运行阶段|INP-日间,EOD-日终,SOD-日始',
  `AUTO_CLIENT_GEN_FLAG` char(1) DEFAULT NULL COMMENT '是否自动生成客户号|是否自动生成客户号|Y-是,N-否',
  `CLIENT_NO_STRUCTURE_TYPE` varchar(3) DEFAULT NULL COMMENT '客户号结构类型|客户号结构类型，目前系统暂未使用',
  `AUTO_COLL_GEN_FLAG` char(1) DEFAULT NULL COMMENT '是否自动生成抵质押编号|是否自动生成抵质押编号|Y-是,N-否',
  `AUTO_LOCK_BL_CLIENT_FLAG` char(1) DEFAULT NULL COMMENT '自动冻结黑名单客户|自动冻结黑名单客户|Y-是,N-否',
  `MULTI_CORPORATION_FLAG` char(1) DEFAULT NULL COMMENT '是否多法人系统|是否多法人系统|Y-是,N-否',
  `MULTI_CORPORATION_METHOD` char(1) DEFAULT NULL COMMENT '多法人机构间清算方式|多法人机构间清算方式|I-系统内清算模式,P-支付清算模式,N-不允许法人间通存通兑',
  `MULTI_ALL_DEP_FLAG` char(1) DEFAULT NULL COMMENT '法人间通存标志|法人间通存标志',
  `MULTI_ALL_DRA_FLAG` char(1) DEFAULT NULL COMMENT '法人间通兑标志|法人间通兑标志',
  `DEP_DRA_TRAN_BRANCH_FLAG` char(1) DEFAULT NULL COMMENT '通存通兑是否过交易行|目前用作跨法人清算时是否过交易所在法人',
  `LOCAL_CCY` varchar(3) DEFAULT NULL COMMENT '当地币种|当地币种',
  `BASE_CCY` varchar(3) DEFAULT NULL COMMENT '基础币种|基础币种',
  `REPORT_CCY` varchar(3) DEFAULT NULL COMMENT '报表币种|报表币种，目前系统暂未使用',
  `LIMIT_CCY` varchar(3) DEFAULT NULL COMMENT '限制币种|限制币种，目前系统暂未使用',
  `DEFAULT_CHARGE_RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '结售汇内部平盘汇率类型|结售汇内部平盘汇率类型',
  `DEFAULT_PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '默认利润中心|默认利润中心',
  `DEFAULT_RATE_TYPE` varchar(10) DEFAULT NULL COMMENT '本地币种汇率类型|本地币种汇率类型',
  `DEFAULT_RATE_TYPE_LOCAL` varchar(10) DEFAULT NULL COMMENT '默认本地汇率类型|默认本地汇率类型',
  `ALLOW_BACKQRY_DAY` int DEFAULT NULL COMMENT '允许查询的历史天数|允许查询的历史天数',
  `BATCH_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '批处理检查标志|批处理检查标志|Y -是,N -否',
  `BATCH_DEFAULT_USER_ID` varchar(30) DEFAULT NULL COMMENT '默认批处理用户|批处理期间系统自动交易的交易柜员',
  `BATCH_MODULE` varchar(2) DEFAULT NULL COMMENT '当前批处理的模块号|当前批处理的模块号，目前系统暂未使用|RB-存款 ,CL-贷款 ,GL-总账',
  `BATCH_UNIT` varchar(50) DEFAULT NULL COMMENT '当前批处理的业务组编号|当前批处理的业务组编号，目前系统暂未使用',
  `CAPITAL_FUNDS` decimal(17,2) DEFAULT NULL COMMENT '投资资金|投资资金',
  `CLIENT_BLOCK_FLAG` char(1) DEFAULT NULL COMMENT '资料不全客户冻结标志|客户资料不全时是否增加客户冻结|Y-是,N-否',
  `CONTINUOUS_RUN` char(1) DEFAULT NULL COMMENT '是否连续使用指定的数字区间标志|是否连续使用指定的数字区间标志，目前系统暂未使用|Y-是,N-否',
  `CR_DR_CHECK_FLAG` char(1) DEFAULT NULL COMMENT '借贷检查标志|借贷检查标志，目前系统暂未使用|Y-是,N-否',
  `EBH_BRANCH` varchar(50) DEFAULT NULL COMMENT '电子银行机构|电子银行机构，目前系统暂未使用',
  `EXCHANGE_RATE_VARIANCE` decimal(5,2) DEFAULT NULL COMMENT '汇率浮动百分比|汇率浮动百分比，目前系统暂未使用',
  `INTERNAL_RATE_CHARGE_FLAG` char(1) DEFAULT NULL COMMENT '是否行内结售汇平盘|是否行内结售汇平盘|Y-是,N-否',
  `INTER_BRANCH_ACCT_HO` varchar(20) DEFAULT NULL COMMENT '分行间清算科目-同业存放|分行间清算科目-同业存放',
  `INTER_BRANCH_IND` char(1) DEFAULT NULL COMMENT '是否为内部银行|是否为内部银行|Y-是,N-否',
  `GAP_TYPE` varchar(20) DEFAULT NULL COMMENT '敞口类型|敞口类型',
  `PROCESS_SPLIT_IND` char(1) NOT NULL COMMENT '批处理阶段标志|系统当前是否处于批处理阶段|Y-批处理阶段,N-非批处理阶段',
  `PRODUCT30E_FLAG` char(1) DEFAULT NULL COMMENT '是否产品版30E计算天数|是否产品版30E计算天数|Y-是使用产品版30E计算天数,N-否使用成都版30E计算天数',
  `GL_IND` char(1) NOT NULL COMMENT 'SYMBOLS总账分离标志|SYMBOLS总账分离标志，目前系统暂未使用|Y-使用SYMBOLS总账,N-不使用SYMBOLS总账',
  `DAC_IND` char(1) DEFAULT NULL COMMENT 'DAC校验标志|DAC校验标志|Y-使用DAC校验,N-不使用DAC校验',
  `IS_DEBUG` char(1) DEFAULT NULL COMMENT '是否记录业务数据信息|是否记录业务数据信息|Y-是,N-否',
  `IS_ERROR` char(1) DEFAULT NULL COMMENT '是否记录出错时的业务数据信息|是否记录出错时的业务数据信息|Y-是,N-否',
  `RB_RESTRAINT_TYPE` varchar(3) DEFAULT NULL COMMENT '默认存款账户限制类型|默认存款账户限制类型',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `DEFAULT_LANGUAGE` varchar(50) DEFAULT NULL COMMENT '默认语言标志|默认语言标志',
  PRIMARY KEY (`COMPANY`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='系统业务参数表|系统业务参数表';

-- ----------------------------
-- Table structure for fm_unload_conf
-- ----------------------------
DROP TABLE IF EXISTS `fm_unload_conf`;
CREATE TABLE `fm_unload_conf` (
  `SYSTEM_ID` varchar(20) NOT NULL COMMENT '系统ID|系统ID',
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `UNLOAD_FLAG` char(1) NOT NULL COMMENT '是否需要卸数|是否需要卸数|1-卸数,0-不卸数',
  `SCREEN_CONDITION` varchar(500) NOT NULL COMMENT '数据筛选条件|查询卸载数据的筛选条件',
  `NODE_ID` varchar(50) DEFAULT NULL COMMENT '数据库节点ID|对于全局参数来说，只需要卸载一个分片库的数据时，这里配上对应的数据源；不配置的话此张表涉及到的数据源都会卸载。',
  `SEGMENT_FLAG` char(1) NOT NULL COMMENT '针对大表，是否需要分段|1-分段,0-不分段',
  `SEGMENT_FIELD` varchar(50) DEFAULT NULL COMMENT '分段字段|分段字段，当SEGMENT_FLAG为1时生效',
  `SEGMENT_SIZE` int DEFAULT NULL COMMENT '分段大小|分段大小',
  `PAGE_SIZE` int DEFAULT NULL COMMENT '分页大小|分页大小',
  `FILE_MERGE_FLAG` char(1) DEFAULT NULL COMMENT '联合贷是否合并文件标志|文件是否合并|Y-是,N-否',
  `FILE_SEGMENT_SIZE` int DEFAULT NULL COMMENT '文件分段大小|文件分段大小',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`TABLE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载参数配置表|数据卸载参数配置';

-- ----------------------------
-- Table structure for fm_unload_file_result
-- ----------------------------
DROP TABLE IF EXISTS `fm_unload_file_result`;
CREATE TABLE `fm_unload_file_result` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `FILE_NAME` varchar(200) NOT NULL COMMENT '文件名称|文件名称',
  `SEGMENT_START` varchar(50) NOT NULL COMMENT '大段起始值|大段起始值',
  `SEGMENT_END` varchar(50) NOT NULL COMMENT '大段终止值|大段终止值',
  `SEGMENT_SIZE` int NOT NULL COMMENT '分段大小|分段大小',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `DEAL_RESULT_FLAG` char(1) NOT NULL COMMENT '处理结果标志|处理结果标志|1-未处理,2-已处理,3-处理中,4-处理失败',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FILE_NAME`,`SEGMENT_START`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载文件结果表|数据卸载文件结果表';

-- ----------------------------
-- Table structure for fm_unload_running
-- ----------------------------
DROP TABLE IF EXISTS `fm_unload_running`;
CREATE TABLE `fm_unload_running` (
  `TABLE_NAME` varchar(50) NOT NULL COMMENT '表名|表名',
  `FILE_NAME` varchar(200) NOT NULL COMMENT '文件名称|文件名称',
  `SEGMENT_START` varchar(50) DEFAULT NULL COMMENT '大段起始值|大段起始值',
  `SEGMENT_END` varchar(50) DEFAULT NULL COMMENT '大段终止值|大段终止值',
  `DEAL_RESULT_FLAG` char(1) NOT NULL COMMENT '处理结果标志|处理结果标志|1-未处理,2-已处理,3-处理中,4-处理失败',
  `FILE_INFO_SUM` varchar(10) DEFAULT NULL COMMENT '文件数据条数|文件数据条数',
  `DEAL_DATE` datetime NOT NULL COMMENT '处理日期|处理日期',
  `REMARK` varchar(200) DEFAULT NULL COMMENT '备注|备注',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`FILE_NAME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='数据卸载运行表|数据卸载运行表';

-- ----------------------------
-- Table structure for fm_user
-- ----------------------------
DROP TABLE IF EXISTS `fm_user`;
CREATE TABLE `fm_user` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|柜员编号，一般复用银行员工编号',
  `USER_NAME` varchar(200) NOT NULL COMMENT '柜员名称|柜员名称',
  `DOCUMENT_ID` varchar(50) DEFAULT NULL COMMENT '证件号码|证件号码',
  `DOCUMENT_TYPE` varchar(3) DEFAULT NULL COMMENT '证件类型|证件类型',
  `USER_TYPE` varchar(20) NOT NULL COMMENT '柜员类别|柜员是普通临柜柜员还是虚拟自助设备柜员|DUMMY_TELLER-虚拟柜员,TELLER_USER-普通柜员',
  `USER_SUB_TYPE` char(1) DEFAULT NULL COMMENT '柜员细类|临柜柜员时细类为Y，虚拟柜员时标记具体的虚拟柜员细类|A-ATM柜员,I-ITM柜员,Q-圈存机柜员,S-系统级虚拟柜员,Y-实体柜员',
  `USER_DESC` varchar(50) DEFAULT NULL COMMENT '柜员描述信息|柜员描述信息，目前系统暂未使用',
  `USER_LANG` varchar(30) DEFAULT NULL COMMENT '柜员语言|用于控制柜员登录柜面后界面的文字显示语种，目前系统暂未使用|E-英文,C-中文',
  `USER_LEVEL` char(1) DEFAULT NULL COMMENT '柜员级别|柜员级别，目前系统暂未使用|0-无级别,1-一级,2-二级,3-三级',
  `BRANCH` varchar(50) NOT NULL COMMENT '所属机构号|柜员在管理上的归属机构',
  `DEPARTMENT` varchar(10) DEFAULT NULL COMMENT '部门|部门，目前系统暂未使用',
  `ROLE_ID` varchar(200) DEFAULT NULL COMMENT '角色|柜员的岗位编号或角色编号，英文逗号分隔，可以给角色上绑定尾箱限额',
  `PROFIT_CENTER` varchar(20) DEFAULT NULL COMMENT '利润中心 |利润中心',
  `ACCT_EXEC` varchar(30) DEFAULT NULL COMMENT '客户经理|客户经理，目前系统暂未使用',
  `TBOOK` varchar(2) DEFAULT NULL COMMENT '账薄|账薄，目前系统暂未使用',
  `ACCOUNT_STATUS` char(1) NOT NULL COMMENT '柜员状态|柜员是正常状态还是已经删除|A-有效,D-删除',
  `APPLICATION_USER_FLAG` char(1) DEFAULT NULL COMMENT '是否应用柜员|是否应用柜员，目前系统暂未使用|Y-是,N-否',
  `EOD_SOD_ENABLED_FLAG` char(1) DEFAULT NULL COMMENT '是否批处理用户|是否批处理用户，目前系统暂未使用|Y-是,N-否',
  `INTER_BRANCH_CL` char(1) DEFAULT NULL COMMENT '是否贷款业务机构|柜员是否可以办理贷款业务|Y-是,N-否',
  `INTER_BRANCH_IND` char(1) DEFAULT NULL COMMENT '是否为内部银行|是否为内部银行，目前系统暂未使用|Y-是,N-否',
  `AUTH_LEVEL` char(1) DEFAULT NULL COMMENT '授权级别|授权级别，目前系统暂未使用|Y-允许授权,N-不允许授权',
  `APPR_USER_ID` varchar(30) DEFAULT NULL COMMENT '复核柜员|复核柜员，目前系统暂未使用',
  `CHECK_DATE` datetime DEFAULT NULL COMMENT '检查日期|检查日期，目前系统暂未使用',
  `CREATION_USER_ID` varchar(30) DEFAULT NULL COMMENT '创建柜员|创建柜员，目前系统暂未使用',
  `MAKE_DATE` datetime DEFAULT NULL COMMENT '柜员创建日期|柜员创建日期',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  `SOURCE_TYPE` varchar(10) DEFAULT NULL COMMENT '渠道类型|柜员创建渠道，目前系统暂未使用',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  PRIMARY KEY (`USER_ID`,`BRANCH`),
  KEY `IDX_FM_USER_2` (`BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='核心柜员信息表|核心柜员信息表';

-- ----------------------------
-- Table structure for fm_user_login_branch
-- ----------------------------
DROP TABLE IF EXISTS `fm_user_login_branch`;
CREATE TABLE `fm_user_login_branch` (
  `USER_ID` varchar(30) NOT NULL COMMENT '交易柜员|柜员ID',
  `LOGIN_BRANCH` varchar(20) NOT NULL COMMENT '可登录机构|默认配置柜员与除本机构之外的可登录的机构的关系数据。如果一个柜员可以登录多个机构，则配置多条关系数据。',
  PRIMARY KEY (`USER_ID`,`LOGIN_BRANCH`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='柜员登录机构表|该表默认配置柜员与除本机构之外的可登录的机构的关系数据。如果一个柜员可以登录多个机构，则配置多条关系数据。';

-- ----------------------------
-- Table structure for fw_tran_info
-- ----------------------------
DROP TABLE IF EXISTS `fw_tran_info`;
CREATE TABLE `fw_tran_info` (
  `SERVICE_ID` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务ID',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务唯一识别号',
  `TRAN_DATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易日期',
  `TRAN_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易时间',
  `IN_MSG` longblob COMMENT '输入报文',
  `OUT_MSG` longblob COMMENT '输出报文',
  `RESPONSE_TYPE` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '输出响应类型',
  `END_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易完成时间',
  `SOURCE_TYPE` varchar(20) DEFAULT NULL,
  `SEQ_NO` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道流水号',
  `PROGRAM_ID` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易屏幕标识',
  `STATUS` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '状态',
  `REFERENCE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务参考号',
  `PLATFORM_ID` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '平台流水号',
  `USER_ID` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作柜员',
  `IP_ADDRESS` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'IP地址',
  `BRANCH_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网点',
  `COMPENSATE_SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '待补偿原交易唯一识别号',
  `WEEK_DAY` decimal(1,0) DEFAULT NULL COMMENT '日期',
  `CREATE_DATE` datetime NOT NULL COMMENT '记录创建日期',
  PRIMARY KEY (`SERVICE_NO`,`CREATE_DATE`) USING BTREE,
  KEY `FW_TRAN_INFO_IDX1` (`TRAN_DATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='交易流水表'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_DATE`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_consumer_msg
-- ----------------------------
DROP TABLE IF EXISTS `mq_consumer_msg`;
CREATE TABLE `mq_consumer_msg` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` datetime NOT NULL COMMENT '生产者接收时间',
  `STATUS` int DEFAULT NULL COMMENT '消息状态：1-接收成功，3:消费成功，4:消费失败',
  `UPDATE_TIME` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新状态时间',
  `REMARK` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`,`RECEIVE_TIME`) USING BTREE,
  KEY `MQ_CONSUMER_MSG_IDX1` (`UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`RECEIVE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_consumer_msg_hist
-- ----------------------------
DROP TABLE IF EXISTS `mq_consumer_msg_hist`;
CREATE TABLE `mq_consumer_msg_hist` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` datetime NOT NULL COMMENT '生产者接收时间',
  `STATUS` int DEFAULT NULL COMMENT '消息状态：1-接收成功，3:消费成功，4:消费失败',
  `UPDATE_TIME` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新状态时间',
  `REMARK` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`,`RECEIVE_TIME`) USING BTREE,
  KEY `MQ_CONSUMER_MSG_HIST_IDX1` (`UPDATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(历史表)(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`RECEIVE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_consumer_repeat
-- ----------------------------
DROP TABLE IF EXISTS `mq_consumer_repeat`;
CREATE TABLE `mq_consumer_repeat` (
  `MESSAGE_ID` varchar(50) NOT NULL COMMENT '消息ID',
  `DESTINATION` varchar(128) DEFAULT NULL COMMENT '消息目的地',
  `RECEIVE_TIME` timestamp NULL DEFAULT NULL COMMENT '生产者接收时间',
  `REMARK` varchar(2000) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`MESSAGE_ID`) USING BTREE,
  KEY `MQ_CONSUMER_REPEAT_IDX1` (`MESSAGE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ消费者信息消息表(UPRIGHT)';

-- ----------------------------
-- Table structure for mq_producer_msg
-- ----------------------------
DROP TABLE IF EXISTS `mq_producer_msg`;
CREATE TABLE `mq_producer_msg` (
  `MESSAGE_ID` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `BROKER_NAME` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'broker名称',
  `OFFSET_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息发送成功，broker生成 id',
  `MESSAGE` longblob COMMENT '消息内容',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新时间',
  `STATUS` int DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成 功；4-异常',
  `MESSAGE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型',
  `SEQ_NO` int DEFAULT NULL COMMENT '消息序列号',
  `QUEUE_ID` int DEFAULT NULL COMMENT '消息接受队列id',
  PRIMARY KEY (`MESSAGE_ID`,`CREATE_TIME`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_IDX` (`FLOW_ID`,`STATUS`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_IDX1` (`LAST_UPDATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者信息消息表(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_producer_msg_hist
-- ----------------------------
DROP TABLE IF EXISTS `mq_producer_msg_hist`;
CREATE TABLE `mq_producer_msg_hist` (
  `MESSAGE_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `BROKER_NAME` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'broker名称',
  `OFFSET_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息发送成功，broker生成 id',
  `MESSAGE` longblob COMMENT '消息内容',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `LAST_UPDATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后一次更新时间',
  `STATUS` int DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成 功；4-异常',
  `MESSAGE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '消息类型',
  `SEQ_NO` int DEFAULT NULL COMMENT '消息序列号',
  `QUEUE_ID` int DEFAULT NULL COMMENT '消息接受队列id',
  PRIMARY KEY (`MESSAGE_ID`,`CREATE_TIME`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_HIST_IDX` (`FLOW_ID`,`STATUS`) USING BTREE,
  KEY `MQ_PRODUCER_MSG_HIST_IDX1` (`LAST_UPDATE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者信息消息表(历史表)(UPRIGHT)'
/*!50100 PARTITION BY RANGE (to_days(`CREATE_TIME`))
(PARTITION p202211 VALUES LESS THAN (738825) ENGINE = InnoDB,
 PARTITION p202212 VALUES LESS THAN (738855) ENGINE = InnoDB,
 PARTITION p202301 VALUES LESS THAN (738886) ENGINE = InnoDB,
 PARTITION p202302 VALUES LESS THAN (738917) ENGINE = InnoDB,
 PARTITION p202303 VALUES LESS THAN (738945) ENGINE = InnoDB,
 PARTITION p202304 VALUES LESS THAN (738976) ENGINE = InnoDB,
 PARTITION p202305 VALUES LESS THAN (739006) ENGINE = InnoDB,
 PARTITION p202306 VALUES LESS THAN (739037) ENGINE = InnoDB,
 PARTITION p202307 VALUES LESS THAN (739067) ENGINE = InnoDB,
 PARTITION p202308 VALUES LESS THAN (739098) ENGINE = InnoDB,
 PARTITION p202309 VALUES LESS THAN (739129) ENGINE = InnoDB,
 PARTITION p202310 VALUES LESS THAN (739159) ENGINE = InnoDB,
 PARTITION p202311 VALUES LESS THAN (739190) ENGINE = InnoDB,
 PARTITION p202312 VALUES LESS THAN (739220) ENGINE = InnoDB,
 PARTITION p202401 VALUES LESS THAN (739251) ENGINE = InnoDB,
 PARTITION p202402 VALUES LESS THAN (739282) ENGINE = InnoDB,
 PARTITION p202403 VALUES LESS THAN (739311) ENGINE = InnoDB,
 PARTITION p202404 VALUES LESS THAN (739342) ENGINE = InnoDB,
 PARTITION p202405 VALUES LESS THAN (739372) ENGINE = InnoDB,
 PARTITION p202406 VALUES LESS THAN (739403) ENGINE = InnoDB,
 PARTITION p202407 VALUES LESS THAN MAXVALUE ENGINE = InnoDB) */;

-- ----------------------------
-- Table structure for mq_producer_send_msg
-- ----------------------------
DROP TABLE IF EXISTS `mq_producer_send_msg`;
CREATE TABLE `mq_producer_send_msg` (
  `MQ_MSG_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '消息id',
  `FLOW_ID` varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
  `STATUS` decimal(11,0) DEFAULT NULL COMMENT '状态:1-消息建立；2-待发送；3-发送成功；4-异常',
  PRIMARY KEY (`MQ_MSG_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='MQ生产者待发送消息表(UPRIGHT)';

-- ----------------------------
-- Table structure for orbit_records
-- ----------------------------
DROP TABLE IF EXISTS `orbit_records`;
CREATE TABLE `orbit_records` (
  `CENTER_IND` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '中心标识',
  `IS_LOCAL` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '本地标识',
  `CACHE_NAME` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '缓存名称',
  `CACHE_KEY` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '缓存KEY',
  PRIMARY KEY (`CENTER_IND`,`IS_LOCAL`,`CACHE_NAME`,`CACHE_KEY`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='分布式缓存组件';

-- ----------------------------
-- Table structure for rc_cb_black_gray_list
-- ----------------------------
DROP TABLE IF EXISTS `rc_cb_black_gray_list`;
CREATE TABLE `rc_cb_black_gray_list` (
  `BATCH_NO` varchar(50) NOT NULL COMMENT '批次号|批次号',
  `SEQ_NO` varchar(50) NOT NULL COMMENT '序号|序号',
  `LOAD_DATE` datetime DEFAULT NULL COMMENT '导入日期|导入日期',
  `OPERATOR` varchar(2) DEFAULT NULL COMMENT '操作符|操作符 :增加/减少 +/-',
  `LIST_CATEGORY` varchar(2) DEFAULT NULL COMMENT '名单种类代码|名单种类代码|01-人行黑名单  ,02-人行灰名单  ,11-行内黑名单  ,12-行内白名单',
  `LIST_TYPE` varchar(10) DEFAULT NULL COMMENT '名单类型代码|名单类型代码|0001-电信诈骗已冻结对私卡号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0002-电信诈骗已冻结存折号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0003-电信诈骗已冻结对公账号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0004-电信诈骗已冻结支付账号需暂停所有业务,0011-公安机关认定为违法犯罪活动，5年内暂停非柜面业务，3年内不得为其新开立账户,0012-买卖银行账户、冒名开户、为违法犯罪活动，5年内暂停非柜面业务，3年内不得为其新开立账户,0021-被全国企业信用信息公示系统列入“严重违法失信企业名单”,0022-经银行核实单位注册地址不存在或者虚构的单位证件号，银行不得为其开户,0031-经过工信部认定无法证明与银行账号绑定合理性的手机号，应当对相关银行账户暂停非柜面业务,0041-中国支付清算协会、银行卡清算机构移交”金融信用信息基础数据库,1001-案件侦办过程中可疑对私卡号需暂停非柜面业务（86号文规定）,1002-案件侦办过程中可疑存折号需暂停非柜面业务（86号文规定）,1003-案件侦办过程中可疑对公账号需暂停非柜面业务（86号文规定）,1004-案件侦办过程中可疑支付账号需风险提示（86号文规定）,2001-行内风险客户名单(限制所有交易),8003-6个月无交易记录',
  `LIST_ORG` varchar(50) DEFAULT NULL COMMENT '名单发送/审核机构|名单发送/审核机构|000000-本行   ,200501-公安,300501-工商总局,400501-高法(暂缓实现),500501-工信部(暂缓实现),600501-中国支付清算协会(暂缓实现),700501-银行卡清算(暂缓实现)',
  `ORG_CODE` varchar(50) DEFAULT NULL COMMENT '银行/支付机构编号|银行/支付机构编号：支付清算系统编号，支付机构以Z开头',
  `ORG_CONTACTOR` varchar(50) DEFAULT NULL COMMENT '发送机构联系人|发送机构联系人',
  `CONTACT_TEL` varchar(50) DEFAULT NULL COMMENT '联系电话  |联系电话',
  `DATA_TYPE` varchar(20) DEFAULT NULL COMMENT '数据类型|黑名单数据类型|AccountNumber-卡/折号,IDType_IDNumber-证件号,ClientNo-客户号,CellPhoneNumber-手机号,IPAddress-IP地址',
  `DATA_VALUE` varchar(50) DEFAULT NULL COMMENT '数据值|数据值',
  `LIST_TYPE_REMARK` varchar(200) DEFAULT NULL COMMENT '名单类型冗余项|名单类型冗余项',
  `ERROR_DESC` varchar(2000) DEFAULT NULL COMMENT '错误描述|错误描述',
  `NAME` varchar(200) DEFAULT NULL COMMENT '名称|名单客户的名称',
  `NARRATIVE` varchar(500) DEFAULT NULL COMMENT '摘要|开户时的账号用途，销户时的销户原因',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态|名单信息状态|A-有效,F-无效',
  `VALID_TERM` varchar(5) DEFAULT NULL COMMENT '有效期|有效期',
  `AUDIT_TIMESTAMP` varchar(26) DEFAULT NULL COMMENT '审计时间|审计时间',
  `CREATE_USER_ID` varchar(30) DEFAULT NULL COMMENT '录入柜员|录入柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BATCH_NO`,`SEQ_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='人行原始黑灰名单表|人行原始黑灰名单表';

-- ----------------------------
-- Table structure for rc_cb_related_acct
-- ----------------------------
DROP TABLE IF EXISTS `rc_cb_related_acct`;
CREATE TABLE `rc_cb_related_acct` (
  `BASE_ACCT_NO` varchar(50) NOT NULL COMMENT '账号/卡号|用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号',
  `LIST_TYPE` varchar(10) DEFAULT NULL COMMENT '名单类型代码|名单类型代码|0001-电信诈骗已冻结对私卡号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0002-电信诈骗已冻结存折号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0003-电信诈骗已冻结对公账号需暂停所有业务(不收不付，禁止换卡，限制关联账号),0004-电信诈骗已冻结支付账号需暂停所有业务,0011-公安机关认定为违法犯罪活动，5年内暂停非柜面业务，3年内不得为其新开立账户,0012-买卖银行账户、冒名开户、为违法犯罪活动，5年内暂停非柜面业务，3年内不得为其新开立账户,0021-被全国企业信用信息公示系统列入“严重违法失信企业名单”,0022-经银行核实单位注册地址不存在或者虚构的单位证件号，银行不得为其开户,0031-经过工信部认定无法证明与银行账号绑定合理性的手机号，应当对相关银行账户暂停非柜面业务,0041-中国支付清算协会、银行卡清算机构移交”金融信用信息基础数据库,1001-案件侦办过程中可疑对私卡号需暂停非柜面业务（86号文规定）,1002-案件侦办过程中可疑存折号需暂停非柜面业务（86号文规定）,1003-案件侦办过程中可疑对公账号需暂停非柜面业务（86号文规定）,1004-案件侦办过程中可疑支付账号需风险提示（86号文规定）,2001-行内风险客户名单(限制所有交易),8003-6个月无交易记录',
  `VERIFY_START_DATE` datetime DEFAULT NULL COMMENT '核实期限起始日期|核实期限起始日期',
  `VERIFY_END_DATE` datetime DEFAULT NULL COMMENT '核实期限终止日期|核实期限终止日期',
  `VERIFY_DATE` datetime DEFAULT NULL COMMENT '确认日期|核实日期',
  `VERIFY_RESULT` char(1) DEFAULT NULL COMMENT '客户核实结果|客户核实结果|N-未核实 ,A-已核实为正常 ,D-已核实为可疑 ,E-已到期未核实 ',
  `USER_ID` varchar(30) DEFAULT NULL COMMENT '交易柜员|交易柜员',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`BASE_ACCT_NO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='待核实人行黑灰名单关联账户表|待核实人行黑灰名单关联账户表';

-- ----------------------------
-- Table structure for sms_type_define
-- ----------------------------
DROP TABLE IF EXISTS `sms_type_define`;
CREATE TABLE `sms_type_define` (
  `SMS_TYPE` varchar(50) NOT NULL COMMENT '短信类型|短信类型|0-短信签约,3-解约短信,01-动账短信',
  `SMS_DESC` varchar(200) NOT NULL COMMENT '短信类型描述|短信类型描述',
  `SMS_TEMPLATE` varchar(500) NOT NULL COMMENT '短信模板|短信模板',
  `SMS_TRAN_TYPE` varchar(10) DEFAULT NULL COMMENT '交易类型范围|交易类型范围',
  `REQUIRE_AGR_FLAG` char(1) DEFAULT NULL COMMENT '是否需要短信签约|是否需要短信签约|A-开启,R-已报盘,G-已清差,S-已清算',
  `CLIENT_TYPE` varchar(3) DEFAULT NULL COMMENT '客户类型|客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE',
  `CLIENT_INDICATOR` char(1) DEFAULT NULL COMMENT '客户标识|客户标识|N-普通客户,S-银行员工客户,V-VIP客户,M-潜在客户',
  `SMS_SEND_TYPE` varchar(50) NOT NULL COMMENT '短信发送类型|短信发送类型',
  `REMIND_DAYS` int DEFAULT NULL COMMENT '提前提醒天数|短信提前提醒天数',
  `SEND_METHOD` char(1) DEFAULT NULL COMMENT '发送方式|发送方式|1-实时,2-定时',
  `SMS_SEND_TIME` varchar(26) DEFAULT NULL COMMENT '短信发送时间|短信发送时间',
  `SMS_MIN_AMT` decimal(17,2) NOT NULL COMMENT '短信发送最小金额|短信发送最小金额',
  `MATCH_CONDITION` varchar(2000) DEFAULT NULL COMMENT '检查内容(匹配条件)|检查内容(匹配条件)',
  `TRAN_TIMESTAMP` varchar(26) NOT NULL COMMENT '交易时间戳|交易时间戳',
  `COMPANY` varchar(20) DEFAULT NULL COMMENT '法人|法人',
  PRIMARY KEY (`SMS_TYPE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='短信类型定义表|短信类型定义表';

-- ----------------------------
-- Table structure for sonic_running_step_lock
-- ----------------------------
DROP TABLE IF EXISTS `sonic_running_step_lock`;
CREATE TABLE `sonic_running_step_lock` (
  `step_run_id` varchar(32) NOT NULL COMMENT 'Step运行时唯一ID',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行者ID',
  `lock_id` varchar(32) DEFAULT NULL COMMENT '锁ID',
  `lock_time` timestamp NULL DEFAULT NULL COMMENT '锁时间戳',
  PRIMARY KEY (`step_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Step执行锁';

-- ----------------------------
-- Table structure for sonic_step_run_result
-- ----------------------------
DROP TABLE IF EXISTS `sonic_step_run_result`;
CREATE TABLE `sonic_step_run_result` (
  `step_run_id` varchar(32) NOT NULL COMMENT 'Step运行时唯一ID',
  `complete_status` varchar(10) DEFAULT NULL COMMENT 'Step完成状态',
  `complete_time` timestamp NULL DEFAULT NULL COMMENT 'Step完成时间',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行者ID',
  PRIMARY KEY (`step_run_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='Step执行结果';

-- ----------------------------
-- Table structure for tran_sql_log
-- ----------------------------
DROP TABLE IF EXISTS `tran_sql_log`;
CREATE TABLE `tran_sql_log` (
  `SERVICE_ID` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务ID',
  `SERVICE_NO` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '服务唯一识别号',
  `TRAN_DATE` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易日期',
  `TRAN_TIME` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易时间',
  `SOURCE_TYPE` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道类型',
  `SEQ_NO` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '渠道流水号',
  `PROGRAM_ID` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '交易屏幕标识',
  `REFERENCE` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务参考号',
  `USER_ID` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '操作柜员',
  `BRANCH_ID` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网点',
  `TABLE_NAME` varchar(50) DEFAULT NULL COMMENT '表名',
  `SQL_TYPE` varchar(10) DEFAULT NULL COMMENT '操作类型（UPDATE/DELETE/INSERT)',
  `BEFORE_EXE_DATA` varchar(4000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '执行前数据',
  `AFTER_EXE_DATA` varchar(4000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '执行后数据',
  `CREATE_DATE` varchar(26) DEFAULT NULL COMMENT '创建时间',
  KEY `TRAN_SQL_LOG_IDX1` (`TRAN_DATE`) USING BTREE,
  KEY `TRAN_SQL_LOG_IDX2` (`REFERENCE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='sql数据登记表';

SET FOREIGN_KEY_CHECKS = 1;
