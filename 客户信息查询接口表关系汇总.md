# 客户信息查询接口表关系汇总

## 涉及表清单

### 核心业务表 (15个)

| 表名 | 中文名称 | 关系类型 | 主要用途 |
|------|----------|----------|----------|
| cif_client | 客户基本信息表 | 主表 | 存储客户基本信息，所有查询的起点 |
| cif_client_indvl | 个人客户扩展表 | 1:1 | 个人客户的详细信息扩展 |
| cif_client_corp | 企业客户扩展表 | 1:1 | 企业客户的详细信息扩展 |
| cif_client_document | 客户证件表 | 1:N | 客户的各种证件信息，支持多证件 |
| cif_client_contact_tbl | 客户联系信息表 | 1:N | 客户的联系方式信息 |
| cif_client_verification | 客户身份核实表 | 1:1 | 个人客户身份核实情况 |
| cif_client_nra | 客户NRA主表 | 1:1 | 客户非居民涉税信息主表 |
| cif_client_nra_detail | 客户NRA详细表 | 1:N | 客户非居民涉税详细信息 |
| cif_client_attach | 客户关联信息表 | 1:1 | 客户的附加关联信息 |
| cif_contact_list | 客户联系人表 | 1:N | 客户的联系人列表 |
| cif_cross_relations | 客户关系表 | N:N | 客户与柜员的关系信息 |
| cif_control_contact | 控制人税收居民联系表 | 1:N | 对公客户控制人信息 |
| cif_crop_compnay_info | 法人财务主管信息表 | 1:N | 对公客户法人和财务主管信息 |
| cif_client_information | 对公客户附属信息表 | 1:1 | 对公客户的附加信息 |
| cif_define_column_information | 自定义字段信息表 | 配置表 | 用户自定义字段配置 |

### 参数配置表 (1个)

| 表名 | 中文名称 | 主要用途 |
|------|----------|----------|
| cif_business_parameter | 业务参数表 | 控制查询行为的业务开关参数 |

## 表关系详细说明

### 1. 主表关系链
```
cif_client (客户主表)
├── cif_client_indvl (个人客户扩展) [1:1]
├── cif_client_corp (企业客户扩展) [1:1]
├── cif_client_document (客户证件) [1:N]
├── cif_client_contact_tbl (客户联系信息) [1:N]
├── cif_client_verification (身份核实) [1:1]
├── cif_client_nra (NRA主表) [1:1]
├── cif_client_nra_detail (NRA详细) [1:N]
├── cif_client_attach (关联信息) [1:1]
├── cif_contact_list (联系人列表) [1:N]
├── cif_cross_relations (客户关系) [N:N]
├── cif_control_contact (控制人联系) [1:N]
├── cif_crop_compnay_info (法人财务信息) [1:N]
└── cif_client_information (对公附属信息) [1:1]
```

### 2. 查询入口关系
- **主键查询**: CLIENT_NO → cif_client
- **三要素查询**: (DOCUMENT_ID + DOCUMENT_TYPE + ISS_COUNTRY) → cif_client_document → CLIENT_NO → cif_client

### 3. 条件分支关系
- **个人客户分支**: CLIENT_TYPE = 个人 → cif_client_indvl + cif_client_verification
- **对公客户分支**: CLIENT_TYPE = 企业 → cif_client_corp + cif_control_contact + cif_crop_compnay_info

### 4. NRA信息关系
```
cif_client_nra (NRA主表)
└── cif_client_nra_detail (NRA详细表) [1:N]
```

## 关键字段关联

### 主键关联
- **CLIENT_NO**: 所有客户相关表的主要关联字段
- **SEQ_NO**: 明细表的序列号字段
- **USER_ID**: 柜员相关的关联字段

### 复合主键
- **cif_client_document**: CLIENT_NO + DOCUMENT_TYPE + DOCUMENT_ID + ISS_COUNTRY
- **cif_client_contact_tbl**: CLIENT_NO + CONTACT_TYPE
- **cif_client_nra_detail**: CLIENT_NO + SEQ_NO
- **cif_cross_relations**: CLIENT_NO + USER_ID

## 查询逻辑特点

### 1. 分层查询策略
1. **第一层**: 客户基本信息查询 (cif_client)
2. **第二层**: 根据客户类型查询扩展信息
3. **第三层**: 查询通用附加信息 (证件、联系方式等)
4. **第四层**: 权限检查和信息处理

### 2. 条件控制机制
- **业务开关控制**: 通过 cif_business_parameter 控制查询行为
- **客户类型分支**: 个人客户和对公客户查询不同的扩展表
- **权限级别控制**: 根据柜员级别决定信息掩码处理

### 3. 数据完整性保证
- **主外键约束**: 通过 CLIENT_NO 保证数据关联完整性
- **业务规则验证**: 客户状态、关系检查等业务规则
- **权限控制**: 柜员与客户关系检查，防止利益冲突

## 性能优化建议

### 1. 索引优化
- **cif_client**: CLIENT_NO (主键索引)
- **cif_client_document**: (DOCUMENT_TYPE, ISS_COUNTRY, DOCUMENT_ID) 复合索引
- **cif_cross_relations**: (CLIENT_NO, USER_ID) 复合索引
- **cif_business_parameter**: PARA_KEY (主键索引)

### 2. 查询优化
- **批量查询**: 尽量减少数据库往返次数
- **条件过滤**: 在数据库层面进行条件过滤
- **字段选择**: 只查询需要的字段，避免全字段查询

### 3. 缓存策略
- **参数缓存**: cif_business_parameter 表数据可以缓存
- **客户基本信息缓存**: 高频查询的客户基本信息可以缓存
- **权限信息缓存**: 柜员权限信息可以缓存

## 业务规则总结

### 1. 查询优先级
1. CLIENT_NO 优先查询
2. 三要素备选查询
3. 特殊逻辑处理

### 2. 权限控制
1. 客户注销状态检查
2. 柜员客户关系检查
3. 信息掩码处理

### 3. 数据完整性
1. 必要信息完整性检查
2. 业务规则验证
3. 数据一致性保证

### 4. 扩展性设计
1. 支持自定义字段
2. 支持多种客户类型
3. 支持灵活的参数配置

## 接口设计优势

1. **完整性**: 覆盖客户信息的各个维度
2. **灵活性**: 支持多种查询方式和客户类型
3. **安全性**: 完善的权限控制和信息保护机制
4. **可配置性**: 通过参数表控制业务行为
5. **可扩展性**: 支持自定义字段和新的业务需求
